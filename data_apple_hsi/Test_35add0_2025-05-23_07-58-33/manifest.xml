<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file type="darkref" extension="raw">capture/DARKREF_Test_35add0_2025-05-23_07-58-33.raw</file>
 <file type="darkref" extension="hdr">capture/DARKREF_Test_35add0_2025-05-23_07-58-33.hdr</file>
 <file type="whiteref" extension="raw">capture/WHITEREF_Test_35add0_2025-05-23_07-58-33.raw</file>
 <file type="whiteref" extension="hdr">capture/WHITEREF_Test_35add0_2025-05-23_07-58-33.hdr</file>
 <file type="capture" extension="raw">capture/Test_35add0_2025-05-23_07-58-33.raw</file>
 <file type="capture" extension="hdr">capture/Test_35add0_2025-05-23_07-58-33.hdr</file>
 <file type="properties" extension="xml">metadata/Test_35add0_2025-05-23_07-58-33.xml</file>
 <file type="properties" extension="xsl">metadata/Test_35add0_2025-05-23_07-58-33.xsl</file>
 <file type="preview" extension="png">Test_35add0_2025-05-23_07-58-33.png</file>
 <file type="reflectance" extension="dat">capture/REFLECTANCE_Test_35add0_2025-05-23_07-58-33.dat</file>
 <file type="reflectance" extension="hdr">capture/REFLECTANCE_Test_35add0_2025-05-23_07-58-33.hdr</file>
</manifest>
