<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file type="darkref" extension="raw">capture/DARKREF_Test_50add0_2025-05-23_07-44-30.raw</file>
 <file type="darkref" extension="hdr">capture/DARKREF_Test_50add0_2025-05-23_07-44-30.hdr</file>
 <file type="whiteref" extension="raw">capture/WHITEREF_Test_50add0_2025-05-23_07-44-30.raw</file>
 <file type="whiteref" extension="hdr">capture/WHITEREF_Test_50add0_2025-05-23_07-44-30.hdr</file>
 <file type="capture" extension="raw">capture/Test_50add0_2025-05-23_07-44-30.raw</file>
 <file type="capture" extension="hdr">capture/Test_50add0_2025-05-23_07-44-30.hdr</file>
 <file type="properties" extension="xml">metadata/Test_50add0_2025-05-23_07-44-30.xml</file>
 <file type="properties" extension="xsl">metadata/Test_50add0_2025-05-23_07-44-30.xsl</file>
 <file type="preview" extension="png">Test_50add0_2025-05-23_07-44-30.png</file>
 <file type="reflectance" extension="dat">capture/REFLECTANCE_Test_50add0_2025-05-23_07-44-30.dat</file>
 <file type="reflectance" extension="hdr">capture/REFLECTANCE_Test_50add0_2025-05-23_07-44-30.hdr</file>
</manifest>
