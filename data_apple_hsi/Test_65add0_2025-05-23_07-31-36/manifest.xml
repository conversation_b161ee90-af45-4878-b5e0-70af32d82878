<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file type="darkref" extension="raw">capture/DARKREF_Test_65add0_2025-05-23_07-31-36.raw</file>
 <file type="darkref" extension="hdr">capture/DARKREF_Test_65add0_2025-05-23_07-31-36.hdr</file>
 <file type="whiteref" extension="raw">capture/WHITEREF_Test_65add0_2025-05-23_07-31-36.raw</file>
 <file type="whiteref" extension="hdr">capture/WHITEREF_Test_65add0_2025-05-23_07-31-36.hdr</file>
 <file type="capture" extension="raw">capture/Test_65add0_2025-05-23_07-31-36.raw</file>
 <file type="capture" extension="hdr">capture/Test_65add0_2025-05-23_07-31-36.hdr</file>
 <file type="properties" extension="xml">metadata/Test_65add0_2025-05-23_07-31-36.xml</file>
 <file type="properties" extension="xsl">metadata/Test_65add0_2025-05-23_07-31-36.xsl</file>
 <file type="preview" extension="png">Test_65add0_2025-05-23_07-31-36.png</file>
 <file type="reflectance" extension="dat">capture/REFLECTANCE_Test_65add0_2025-05-23_07-31-36.dat</file>
 <file type="reflectance" extension="hdr">capture/REFLECTANCE_Test_65add0_2025-05-23_07-31-36.hdr</file>
</manifest>
