# dataset.py

import os
import numpy as np
import scipy.io as sio
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from utils import *
import tifffile as tiff

def load_HSI(file_path):
    """加载高光谱图像数据"""
    return sio.loadmat(file_path)["HSI"]

def load_point_cloud(file_path):
    """加载点云数据并转换为 2D 和 3D 点阵"""
    resized_points_3D = np.load(file_path)['matrix']
    resized_points_2D = resized_points_3D.reshape(-1, 3)
    return resized_points_2D, resized_points_3D

def downsample_HSI(HSI, target_size=(79, 82, 273)):
    """对高光谱图像进行降采样"""
    return Downsampling_HSI(HSI, target_size)

def visualize_data(HSI_resized, resized_points_2D, band_index=110):
    """可视化点云和灰度图"""
    # 可视化点云散点图
    # visualize_point_cloud(resized_points_2D)
    # 可视化3D和灰度图
    visualize_3D_grayscale(HSI_resized, resized_points_2D, band_index)

# 找到平坦区域作为真实光谱
def preprocess_HSI(HSI_resized, crop_coords=(20, 30, 20, 30), select_true_spectrum = "dian"):
    """预处理高光谱图像和生成真实光谱"""
    # HSI_resized_SNV = np.array(SNV(SG(HSI_resized.reshape(H*W,B))))
    HSI_resized_SNV = np.array(SG(HSI_resized.reshape(-1, HSI_resized.shape[2]))) / 10000
    HSI_resized_SNV = HSI_resized_SNV.reshape(HSI_resized.shape)
    crop_start_x, crop_end_x, crop_start_y, crop_end_y = crop_coords
    # 剪裁
    HSI_GT = HSI_resized_SNV[crop_start_x:crop_end_x, crop_start_y:crop_end_y, :]
    # 计算标准真实光谱 取均值
    if select_true_spectrum == "xian":
        # true xian
        true_spectrum = np.mean(HSI_GT, axis=1)

        # 绘制真实光谱
        plt.figure(figsize=(10, 5))
        plt.plot(true_spectrum, color='blue')
        plt.title('Spectrum at 0° SNV')
        plt.xlabel('Wavelength Band Index')
        plt.ylabel('Reflectance (SNV)')
        plt.legend()
        plt.grid()
        plt.show()

    elif select_true_spectrum == "dian":
        # true dian
        true_spectrum = np.mean(HSI_GT, axis=(0, 1))

    return HSI_resized_SNV, true_spectrum

# 根据深度阈值给出真实光谱
def assign_target_spectrum_true_dian(resized_points_2D, true_spectrum, sun, label_image, depth_threshold=0):
    """
    根据深度值分配真实光谱。深度大于 depth_threshold 的样本分配真实光谱，深度等于 depth_threshold 的样本分配全零光谱。
    标签为1的位置（损伤位置）光谱被sun光谱替换。

    参数:
        resized_points_2D (ndarray): 点云数据，形状为 (105*88, 3)。
        true_spectrum (ndarray): 光谱数据，形状为 (273,)。
        sun (ndarray): 用于替换损伤位置的光谱数据，形状为 (273,)。
        depth_threshold (float): 深度阈值，等于此值的点分配全零光谱。

    返回:
        spectrum_GT (ndarray): 分配后的真实光谱，形状为 (105*88, 273)。
    """

    sun = np.array(SG(sun)) / 10000
    sun1 = sun.flatten()
    num_rows = label_image.shape[0]
    num_cols = label_image.shape[1]
    num_bands = true_spectrum.shape[0]

    # 绘制真实光谱
    plt.figure(figsize=(10, 5))
    plt.plot(true_spectrum, color='blue', label="zhengchang")
    plt.plot(sun1, color='red', label="sun")
    plt.title('Spectrum at 0° SNV')
    plt.xlabel('Wavelength Band Index')
    plt.ylabel('Reflectance (SNV)')
    plt.legend()
    plt.grid()
    plt.show()


    # 初始化真实光谱矩阵为 0，大小为 (105*88, 273)
    spectrum_GT = np.zeros((num_rows * num_cols, num_bands))

    # 遍历每一行 true_spectrum，将其赋值给 GT 中对应位置
    for i in range(num_rows):
        for j in range(num_cols):
            index = i * num_cols + j
            if resized_points_2D[index, 2] > depth_threshold:  # 检查深度
                # spectrum_GT[index] = true_spectrum[i]  # 分配 true_spectrum 的第 i 行到 GT 的对应位置
                if label_image[i, j] == 1:
                    spectrum_GT[index] = sun  # 损伤位置用 sun 光谱填充
                else:
                    spectrum_GT[index] = true_spectrum  # 其他位置分配 true_spectrum 的光谱

    spectrum_GT1 = spectrum_GT.reshape(num_rows, num_cols, 273)
    plot_hyperspectral_band(spectrum_GT1, 33)

    return spectrum_GT


# 根据深度阈值给出真实光谱
def assign_target_spectrum_true_xian(resized_points_2D, true_spectrum, sun, depth_threshold=0):
    """
      根据深度值分配真实光谱。深度大于 depth_threshold 的样本分配光谱数据，
      深度等于 depth_threshold 的样本分配全零光谱。

      参数:
          resized_points_2D (ndarray): 点云数据，形状为 (105*88, 3)。
          true_spectrum (ndarray): 光谱数据，形状为 (105, 273)。
          depth_threshold (float): 深度阈值，等于此值的点分配全零光谱。

      返回:
          spectrum_GT (ndarray): 分配后的真实光谱，形状为 (105*88, 273)。
      """
    num_rows = true_spectrum.shape[0]
    num_cols = resized_points_2D.shape[0] // num_rows
    num_bands = true_spectrum.shape[1]

    # 初始化真实光谱矩阵为 0，大小为 (105*88, 273)
    spectrum_GT = np.zeros((num_rows * num_cols, num_bands))
    label_image = sio.loadmat("data/241111/2_apple/label.mat")['label']
    sun = np.array(SG(sun)) / 10000
    # 遍历每一行 true_spectrum，将其赋值给 GT 中对应位置
    for i in range(num_rows):
        for j in range(num_cols):
            index = i * num_cols + j
            if resized_points_2D[index, 2] > depth_threshold:  # 检查深度
                # spectrum_GT[index] = true_spectrum[i]  # 分配 true_spectrum 的第 i 行到 GT 的对应位置
                if label_image[i, j] == 1:
                    spectrum_GT[index] = sun  # 损伤位置用 sun 光谱填充
                else:
                    spectrum_GT[index] = true_spectrum[i]  # 其他位置分配 true_spectrum 的光谱

    spectrum_GT1 = spectrum_GT.reshape(105, 88, 273)
    plot_hyperspectral_band(spectrum_GT1, 33)

    return spectrum_GT


def compute_features(resized_points_2D, HSI_resized_SNV, spectrum_GT):
    """计算各种特征，包括密度、法向量和角度"""

    print("Z 坐标的最小值:", resized_points_2D[:, 2].min())
    print("Z 坐标的最大值:", resized_points_2D[:, 2].max())
    print("Z 轴的标准差:", np.std(resized_points_2D[:, 2]))

    # 密度指标
    densities = compute_density(resized_points_2D, radius=0.1)
    average_density = np.mean(densities)
    # 0.01比较稀疏 radius5 max_nn50
    print("平均邻域点数（密度指标）:", average_density)

    #### 计算法向量
    # pca拟合平面计算法向量
    # normals = compute_normals_manually(resized_points_2D, 5)

    # 基于 KD 树的邻域搜索法计算法向量
    # 较大的radius值（1-5） 增大max_nn（如 40-50）
    normals = compute_normals_with_adjusted_radius(resized_points_2D, 5, 30)
    # 可视化带法向量的点云
    visualize_normals(resized_points_2D, normals)
    angles, orientations, inclination = compute_inclination_orientation_inclination(normals)
    change_rate = calculate_change_rate_with_depth(HSI_resized_SNV, spectrum_GT.reshape(HSI_resized_SNV.shape[0], HSI_resized_SNV.shape[1], 273), resized_points_2D[:, 2]
                                                   .reshape(HSI_resized_SNV.shape[0], HSI_resized_SNV.shape[1]))
    SAD = calculate_spectral_angle_distance(HSI_resized_SNV, spectrum_GT.reshape(HSI_resized_SNV.shape[0], HSI_resized_SNV.shape[1], 273), resized_points_2D[:, 2]
                                            .reshape(HSI_resized_SNV.shape[0], HSI_resized_SNV.shape[1]))
    return angles, orientations, inclination, change_rate, SAD


def prepare_datasets_row(HSI_resized, resized_points_2D, crop_coords, sun, select_true_spectrum, depth_threshold=0):
    """合并特征，划分训练集和测试集"""

    # 高光谱图像预处理 得到一条中间区域光谱
    HSI_resized_SNV, true_spectrum = preprocess_HSI(HSI_resized, crop_coords, select_true_spectrum)
    H, W, B = HSI_resized_SNV.shape

    # 根据深度阈值给出真实光谱
    if select_true_spectrum == "xian":
        # true xian
        spectrum_GT = assign_target_spectrum_true_xian(resized_points_2D, true_spectrum, sun, depth_threshold=0)
    elif select_true_spectrum == "dian":
        # true dian
        spectrum_GT = assign_target_spectrum_true_dian(resized_points_2D, true_spectrum, sun, depth_threshold=0)

    angles, orientations, inclination, change_rate, SAD = compute_features(resized_points_2D, HSI_resized_SNV, spectrum_GT)

    # print("angles输入最大值:", angles.max())
    # print("angles输入最小值:", angles.min())
    # print("angles是否包含 NaN:", np.isnan(angles).any())
    # print("angles是否包含 Inf:", np.isinf(angles).any())

    # 合并输入特征
    X = np.column_stack((angles, orientations, inclination, HSI_resized_SNV.reshape(H * W, B)))
    # 基于深度值分配真实光谱
    y = spectrum_GT
    # y = np.tile(true_spectrum, (H * W, 1))

    # ----划分训练集和测试集(按行2：1划分)
    split_index_row = (H * W) // 3 * 2
    X_train, X_test = X[:split_index_row], X[split_index_row:]
    y_train, y_test = y[:split_index_row], y[split_index_row:]


    # 从 resized_points_2D 中获取训练集部分的深度信息(行)
    depth_train = resized_points_2D[:split_index_row, 2]
    # 使用深度不为 0 的索引筛选训练集样本
    non_zero_depth_indices_train = depth_train != 0
    X_train = X_train[non_zero_depth_indices_train]
    y_train = y_train[non_zero_depth_indices_train]

    # 获取测试集的深度信息，并筛选深度不为 0 的样本
    depth_test = resized_points_2D[split_index_row:, 2]
    non_zero_depth_indices_test = depth_test != 0
    X_test = X_test[non_zero_depth_indices_test]
    y_test = y_test[non_zero_depth_indices_test]

    # 打乱训练集的顺序
    shuffle_indices = np.random.permutation(X_train.shape[0])
    X_train, y_train = X_train[shuffle_indices], y_train[shuffle_indices]

    return X_train, X_test, y_train, y_test, non_zero_depth_indices_test, split_index_row//W

def prepare_datasets_col(HSI_resized, resized_points_2D, crop_coords, sun, select_true_spectrum, depth_threshold=0):
    """合并特征，划分训练集和测试集"""

    # 高光谱图像预处理 得到一条中间区域光谱
    HSI_resized_SNV, true_spectrum = preprocess_HSI(HSI_resized, crop_coords, select_true_spectrum)
    H, W, B = HSI_resized_SNV.shape

    # 根据深度阈值给出真实光谱
    if select_true_spectrum == "xian":
        # true xian
        spectrum_GT = assign_target_spectrum_true_xian(resized_points_2D, true_spectrum, sun, depth_threshold=0)
    elif select_true_spectrum == "dian":
        # true dian
        spectrum_GT = assign_target_spectrum_true_dian(resized_points_2D, true_spectrum, sun, depth_threshold=0)

    angles, orientations, inclination, change_rate, SAD = compute_features(resized_points_2D, HSI_resized_SNV, spectrum_GT)

    # print("angles输入最大值:", angles.max())
    # print("angles输入最小值:", angles.min())
    # print("angles是否包含 NaN:", np.isnan(angles).any())
    # print("angles是否包含 Inf:", np.isinf(angles).any())

    # 合并输入特征
    X = np.column_stack((angles, orientations, inclination, HSI_resized_SNV.reshape(H * W, B)))
    # 基于深度值分配真实光谱
    y = spectrum_GT

    B1= X.shape[1]
    X = X.reshape(H, W, B1)
    y = y.reshape(H, W, B)

    # 划分列索引 3:1，即前 66 列为训练集，后 22 列为测试集
    split_column_index = W * 3 // 4
    # 划分训练集和测试集
    X_train, X_test = X[:, :split_column_index, :], X[:, split_column_index:, :]
    y_train, y_test = y[:, :split_column_index, :], y[:, split_column_index:, :]

    # 将训练集和测试集展平为二维矩阵
    X_train = X_train.reshape(-1, B1)  # (105 * 66, 276)
    X_test = X_test.reshape(-1, B1)  # (105 * 22, 276)
    y_train = y_train.reshape(-1, B)  # (105 * 66, 276)
    y_test = y_test.reshape(-1, B)  # (105 * 22, 276)

    # 获取训练集部分的深度信息，并筛选深度不为 0 的样本
    depth = resized_points_2D.reshape(H, W, 3)[:, :split_column_index, 2].reshape(-1)
    non_zero_depth_indices = depth != 0
    X_train = X_train[non_zero_depth_indices]
    y_train = y_train[non_zero_depth_indices]

    # 获取测试集的深度信息，并筛选深度不为 0 的样本
    depth_test = resized_points_2D.reshape(H, W, 3)[:, split_column_index:, 2].reshape(-1)
    non_zero_depth_indices_test = depth_test != 0
    X_test = X_test[non_zero_depth_indices_test]
    y_test = y_test[non_zero_depth_indices_test]

    # 打乱训练集的顺序
    shuffle_indices = np.random.permutation(X_train.shape[0])
    X_train, y_train = X_train[shuffle_indices], y_train[shuffle_indices]

    return X_train, X_test, y_train, y_test, non_zero_depth_indices_test, split_column_index

# 只准备训练集
def prepare_single_dataset_train(HSI_resized, resized_points_2D, crop_coords, sun, label_image, depth_threshold=0):
    """仅准备单一训练集"""
    HSI_resized_SNV, true_spectrum = preprocess_HSI(HSI_resized, crop_coords)
    H, W, B = HSI_resized_SNV.shape
    spectrum_GT = assign_target_spectrum_true_dian(resized_points_2D, true_spectrum, sun, label_image, depth_threshold)

    angles, orientations, inclination, change_rate, SAD = compute_features(resized_points_2D, HSI_resized_SNV, spectrum_GT)

    X = np.column_stack((angles, orientations, inclination, HSI_resized_SNV.reshape(H * W, B)))
    y = spectrum_GT.reshape(-1, B)

    depth = resized_points_2D[:, 2]
    non_zero_depth_indices = depth != 0
    X = X[non_zero_depth_indices]
    y = y[non_zero_depth_indices]

    shuffle_indices = np.random.permutation(X.shape[0])
    X, y = X[shuffle_indices], y[shuffle_indices]

    return X, y

def prepare_single_dataset_test(HSI_resized, resized_points_2D, crop_coords, sun, label_image, depth_threshold=0):
    """仅准备单一训练集"""
    HSI_resized_SNV, true_spectrum = preprocess_HSI(HSI_resized, crop_coords)
    H, W, B = HSI_resized_SNV.shape
    spectrum_GT = assign_target_spectrum_true_dian(resized_points_2D, true_spectrum, sun, label_image, depth_threshold)

    angles, orientations, inclination, change_rate, SAD = compute_features(resized_points_2D, HSI_resized_SNV, spectrum_GT)

    X = np.column_stack((angles, orientations, inclination, HSI_resized_SNV.reshape(H * W, B)))
    y = spectrum_GT.reshape(-1, B)

    depth = resized_points_2D[:, 2]
    non_zero_depth_indices = depth != 0
    X = X[non_zero_depth_indices]
    y = y[non_zero_depth_indices]

    return X, y, non_zero_depth_indices