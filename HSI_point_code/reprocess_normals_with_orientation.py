#!/usr/bin/env python3
"""
重新处理融合数据的法向量，应用方向一致性算法
解决边缘区域法向量方向相反的问题
"""

import numpy as np
import pandas as pd
import os
from compute_fusion_normals import (
    load_fusion_data, 
    compute_fusion_normals, 
    save_fusion_normals,
    visualize_fusion_normals
)

def reprocess_single_file(csv_file_path, method='open3d', radius=5.0, max_nn=30,
                         orient_normals=True, reference_direction=None, 
                         compare_with_original=True):
    """
    重新处理单个融合数据文件的法向量
    
    参数:
        csv_file_path (str): 原始CSV文件路径
        method (str): 计算方法
        radius (float): 邻域搜索半径
        max_nn (int): 最大邻居数量
        orient_normals (bool): 是否进行方向一致性处理
        reference_direction (np.ndarray): 参考方向
        compare_with_original (bool): 是否与原始结果比较
        
    返回:
        str: 新的输出文件路径
    """
    print(f"\n{'='*80}")
    print(f"重新处理文件: {csv_file_path}")
    print(f"{'='*80}")
    
    if reference_direction is None:
        reference_direction = np.array([0, 0, 1])  # 默认向上
    
    # 加载数据
    points, spectral_data, metadata = load_fusion_data(csv_file_path)
    
    # 重新计算法向量（带方向一致性处理）
    print("\n--- 重新计算法向量（带方向一致性处理）---")
    normals_new = compute_fusion_normals(
        points, 
        method=method, 
        radius=radius, 
        max_nn=max_nn,
        orient_normals=orient_normals, 
        reference_direction=reference_direction
    )
    
    # 如果需要比较，加载原始法向量
    if compare_with_original:
        original_normals_file = csv_file_path.replace('.csv', '_with_normals.csv')
        if os.path.exists(original_normals_file):
            print(f"\n--- 与原始结果比较 ---")
            original_df = pd.read_csv(original_normals_file)
            normals_original = original_df[['normal_x', 'normal_y', 'normal_z']].values
            
            # 比较法向量方向
            compare_normal_orientations(normals_original, normals_new, points)
        else:
            print(f"原始法向量文件不存在: {original_normals_file}")
    
    # 保存新的结果
    base_name = os.path.splitext(csv_file_path)[0]
    output_file = f"{base_name}_with_normals_oriented.csv"
    
    print(f"\n--- 保存结果 ---")
    print(f"输出文件: {output_file}")
    
    # 创建新的DataFrame
    result_df = metadata.copy()
    
    # 添加点云坐标
    result_df['x'] = points[:, 0]
    result_df['y'] = points[:, 1]
    result_df['z'] = points[:, 2]
    
    # 添加新的法向量
    result_df['normal_x'] = normals_new[:, 0]
    result_df['normal_y'] = normals_new[:, 1]
    result_df['normal_z'] = normals_new[:, 2]
    
    # 计算法向量的角度信息
    from utils import compute_inclination_orientation_inclination
    angles, orientations, inclination = compute_inclination_orientation_inclination(normals_new)
    result_df['normal_angle_with_z'] = angles
    result_df['normal_orientation_xy'] = orientations
    result_df['normal_inclination'] = inclination
    
    # 添加光谱数据
    original_df = pd.read_csv(csv_file_path)
    spectral_columns = [col for col in original_df.columns if col.startswith('band_')]
    for i, col in enumerate(spectral_columns):
        result_df[col] = spectral_data[:, i]
    
    # 保存到CSV文件
    result_df.to_csv(output_file, index=False)
    print(f"保存完成: {output_file}")
    
    return output_file

def compare_normal_orientations(normals_original, normals_new, points):
    """
    比较原始法向量和新法向量的方向差异
    """
    # 计算法向量之间的点积
    dot_products = np.sum(normals_original * normals_new, axis=1)
    
    # 统计方向相反的点（点积为负）
    opposite_mask = dot_products < 0
    opposite_count = np.sum(opposite_mask)
    
    print(f"法向量方向比较:")
    print(f"  - 总点数: {len(normals_original)}")
    print(f"  - 方向相反的点数: {opposite_count}")
    print(f"  - 方向相反的比例: {opposite_count/len(normals_original)*100:.2f}%")
    
    if opposite_count > 0:
        # 分析方向相反的点的分布
        center = np.mean(points, axis=0)
        distances_to_center = np.linalg.norm(points - center, axis=1)
        
        # 边缘点的定义（距离中心超过80%最大距离）
        max_distance = np.max(distances_to_center)
        edge_threshold = 0.8 * max_distance
        edge_mask = distances_to_center > edge_threshold
        
        # 统计边缘和中心区域的方向相反情况
        edge_opposite = np.sum(opposite_mask & edge_mask)
        center_opposite = np.sum(opposite_mask & ~edge_mask)
        
        print(f"  - 边缘区域方向相反: {edge_opposite}/{np.sum(edge_mask)} ({edge_opposite/max(np.sum(edge_mask), 1)*100:.2f}%)")
        print(f"  - 中心区域方向相反: {center_opposite}/{np.sum(~edge_mask)} ({center_opposite/max(np.sum(~edge_mask), 1)*100:.2f}%)")
        
        # 计算角度差异
        angles = np.degrees(np.arccos(np.clip(np.abs(dot_products), 0, 1)))
        print(f"  - 平均角度差异: {np.mean(angles):.2f}°")
        print(f"  - 最大角度差异: {np.max(angles):.2f}°")

def reprocess_all_fusion_files(fusion_dir="../fusion_data", method='open3d', 
                              radius=5.0, max_nn=30, orient_normals=True,
                              reference_direction=None):
    """
    重新处理所有融合数据文件的法向量
    """
    print(f"开始重新处理所有融合数据文件...")
    print(f"目录: {fusion_dir}")
    print(f"方向一致性处理: {orient_normals}")
    
    if reference_direction is None:
        reference_direction = np.array([0, 0, 1])
    
    # 查找所有原始融合数据文件
    fusion_files = []
    for file in os.listdir(fusion_dir):
        if (file.startswith('hsi_point_fusion_') and 
            file.endswith('.csv') and 
            'metadata' not in file and
            'with_normals' not in file):  # 排除已处理的文件
            fusion_files.append(os.path.join(fusion_dir, file))
    
    fusion_files.sort()
    print(f"找到 {len(fusion_files)} 个原始融合数据文件")
    
    # 处理每个文件
    output_files = []
    for csv_file in fusion_files:
        try:
            output_file = reprocess_single_file(
                csv_file, 
                method=method, 
                radius=radius, 
                max_nn=max_nn,
                orient_normals=orient_normals, 
                reference_direction=reference_direction,
                compare_with_original=True
            )
            output_files.append(output_file)
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n{'='*80}")
    print(f"重新处理完成!")
    print(f"成功处理 {len(output_files)} 个文件")
    print(f"输出文件:")
    for output_file in output_files:
        print(f"  - {output_file}")
    print(f"{'='*80}")
    
    return output_files

if __name__ == "__main__":
    # 重新处理所有文件，应用方向一致性算法
    print("重新处理融合数据的法向量，应用方向一致性算法")
    print("这将解决边缘区域法向量方向相反的问题")
    
    # 设置参考方向（向上）
    reference_direction = np.array([0, 0, 1])
    
    # 重新处理所有文件
    output_files = reprocess_all_fusion_files(
        fusion_dir="../fusion_data",
        method='open3d',
        radius=5.0,
        max_nn=30,
        orient_normals=True,
        reference_direction=reference_direction
    )
    
    print(f"\n处理完成！生成了 {len(output_files)} 个方向一致的法向量文件。")
    print("新文件名包含 '_oriented' 后缀，以区别于原始处理结果。")
