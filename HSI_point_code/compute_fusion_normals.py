import pandas as pd
import numpy as np
import os
from utils import compute_normals_with_adjusted_radius, compute_normals_manually, visualize_normals
from utils import compute_inclination_orientation_inclination, calculate_inclination_and_direction
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_fusion_data(csv_file_path):
    """
    从融合数据CSV文件中加载点云坐标和光谱数据
    
    参数:
        csv_file_path (str): CSV文件路径
        
    返回:
        tuple: (points, spectral_data, metadata)
            - points: 点云坐标 (N, 3)
            - spectral_data: 光谱数据 (N, num_bands)
            - metadata: 包含行列信息的DataFrame
    """
    print(f"正在加载融合数据: {csv_file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path)
    
    # 提取点云坐标 (x, y, z)
    points = df[['x', 'y', 'z']].values
    
    # 提取光谱数据（所有以band_开头的列）
    spectral_columns = [col for col in df.columns if col.startswith('band_')]
    spectral_data = df[spectral_columns].values
    
    # 提取元数据（行列信息）
    metadata = df[['row', 'col']].copy()
    
    print(f"加载完成:")
    print(f"  - 点云数量: {len(points)}")
    print(f"  - 光谱波段数: {len(spectral_columns)}")
    print(f"  - 点云坐标范围:")
    print(f"    X: [{points[:, 0].min():.3f}, {points[:, 0].max():.3f}]")
    print(f"    Y: [{points[:, 1].min():.3f}, {points[:, 1].max():.3f}]")
    print(f"    Z: [{points[:, 2].min():.3f}, {points[:, 2].max():.3f}]")
    
    return points, spectral_data, metadata

def compute_fusion_normals(points, method='open3d', radius=5.0, max_nn=30):
    """
    计算融合数据点云的法向量
    
    参数:
        points (np.ndarray): 点云坐标 (N, 3)
        method (str): 计算方法 ('open3d' 或 'manual')
        radius (float): 邻域搜索半径
        max_nn (int): 最大邻居数量
        
    返回:
        np.ndarray: 法向量 (N, 3)
    """
    print(f"正在计算法向量...")
    print(f"  - 方法: {method}")
    print(f"  - 半径: {radius}")
    print(f"  - 最大邻居数: {max_nn}")
    
    if method == 'open3d':
        normals = compute_normals_with_adjusted_radius(points, radius=radius, max_nn=max_nn)
    elif method == 'manual':
        normals = compute_normals_manually(points, radius=radius)
    else:
        raise ValueError(f"未知的计算方法: {method}")
    
    print(f"法向量计算完成")
    print(f"  - 法向量数量: {len(normals)}")
    print(f"  - 法向量范围:")
    print(f"    Nx: [{normals[:, 0].min():.3f}, {normals[:, 0].max():.3f}]")
    print(f"    Ny: [{normals[:, 1].min():.3f}, {normals[:, 1].max():.3f}]")
    print(f"    Nz: [{normals[:, 2].min():.3f}, {normals[:, 2].max():.3f}]")
    
    return normals

def save_fusion_normals(csv_file_path, points, normals, spectral_data, metadata):
    """
    保存包含法向量的融合数据到新的CSV文件
    
    参数:
        csv_file_path (str): 原始CSV文件路径
        points (np.ndarray): 点云坐标 (N, 3)
        normals (np.ndarray): 法向量 (N, 3)
        spectral_data (np.ndarray): 光谱数据 (N, num_bands)
        metadata (pd.DataFrame): 元数据
    """
    # 创建输出文件名
    base_name = os.path.splitext(csv_file_path)[0]
    output_file = f"{base_name}_with_normals.csv"
    
    print(f"正在保存结果到: {output_file}")
    
    # 创建新的DataFrame
    result_df = metadata.copy()
    
    # 添加点云坐标
    result_df['x'] = points[:, 0]
    result_df['y'] = points[:, 1]
    result_df['z'] = points[:, 2]
    
    # 添加法向量
    result_df['normal_x'] = normals[:, 0]
    result_df['normal_y'] = normals[:, 1]
    result_df['normal_z'] = normals[:, 2]
    
    # 计算法向量的角度信息
    angles, orientations, inclination = compute_inclination_orientation_inclination(normals)
    result_df['normal_angle_with_z'] = angles  # 与Z轴的夹角
    result_df['normal_orientation_xy'] = orientations  # XY平面上的方向角
    result_df['normal_inclination'] = inclination  # 倾斜角
    
    # 添加光谱数据
    original_df = pd.read_csv(csv_file_path)
    spectral_columns = [col for col in original_df.columns if col.startswith('band_')]
    for i, col in enumerate(spectral_columns):
        result_df[col] = spectral_data[:, i]
    
    # 保存到CSV文件
    result_df.to_csv(output_file, index=False)
    print(f"保存完成: {output_file}")
    
    return output_file

def visualize_fusion_normals(points, normals, title="融合数据法向量可视化", save_path=None):
    """
    可视化融合数据的点云和法向量
    
    参数:
        points (np.ndarray): 点云坐标 (N, 3)
        normals (np.ndarray): 法向量 (N, 3)
        title (str): 图像标题
        save_path (str): 保存路径（可选）
    """
    print("正在生成法向量可视化...")
    
    # 使用Open3D可视化（如果可用）
    try:
        visualize_normals(points, normals)
    except Exception as e:
        print(f"Open3D可视化失败: {e}")
        print("使用matplotlib进行可视化...")
        
        # 使用matplotlib进行可视化
        fig = plt.figure(figsize=(15, 5))
        
        # 3D散点图显示点云
        ax1 = fig.add_subplot(131, projection='3d')
        scatter = ax1.scatter(points[:, 0], points[:, 1], points[:, 2], 
                             c=points[:, 2], cmap='viridis', s=1)
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        ax1.set_zlabel('Z')
        ax1.set_title('点云数据')
        plt.colorbar(scatter, ax=ax1, shrink=0.5)
        
        # 法向量的XY分量
        ax2 = fig.add_subplot(132)
        quiver = ax2.quiver(points[:, 0], points[:, 1], 
                           normals[:, 0], normals[:, 1], 
                           scale=10, alpha=0.7)
        ax2.set_xlabel('X')
        ax2.set_ylabel('Y')
        ax2.set_title('法向量XY分量')
        ax2.axis('equal')
        
        # 法向量角度分布
        ax3 = fig.add_subplot(133)
        angles, _, _ = compute_inclination_orientation_inclination(normals)
        ax3.hist(angles, bins=50, alpha=0.7, edgecolor='black')
        ax3.set_xlabel('与Z轴夹角 (度)')
        ax3.set_ylabel('频次')
        ax3.set_title('法向量角度分布')
        
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        else:
            plt.show()

def process_fusion_file(csv_file_path, method='open3d', radius=5.0, max_nn=30, visualize=True):
    """
    处理单个融合数据文件，计算法向量并保存结果
    
    参数:
        csv_file_path (str): CSV文件路径
        method (str): 计算方法
        radius (float): 邻域搜索半径
        max_nn (int): 最大邻居数量
        visualize (bool): 是否进行可视化
        
    返回:
        str: 输出文件路径
    """
    print(f"\n{'='*60}")
    print(f"处理文件: {csv_file_path}")
    print(f"{'='*60}")
    
    # 加载数据
    points, spectral_data, metadata = load_fusion_data(csv_file_path)
    
    # 计算法向量
    normals = compute_fusion_normals(points, method=method, radius=radius, max_nn=max_nn)
    
    # 保存结果
    output_file = save_fusion_normals(csv_file_path, points, normals, spectral_data, metadata)
    
    # 可视化
    if visualize:
        base_name = os.path.splitext(csv_file_path)[0]
        vis_path = f"{base_name}_normals_visualization.png"
        visualize_fusion_normals(points, normals, 
                                title=f"法向量可视化 - {os.path.basename(csv_file_path)}", 
                                save_path=vis_path)
    
    return output_file

def process_all_fusion_files(fusion_dir="../fusion_data", method='open3d', radius=5.0, max_nn=30):
    """
    处理fusion_data目录下的所有融合数据文件

    参数:
        fusion_dir (str): 融合数据目录
        method (str): 计算方法
        radius (float): 邻域搜索半径
        max_nn (int): 最大邻居数量
    """
    print(f"开始批量处理融合数据文件...")
    print(f"目录: {fusion_dir}")

    # 查找所有融合数据文件
    fusion_files = []
    for file in os.listdir(fusion_dir):
        if file.startswith('hsi_point_fusion_') and file.endswith('.csv') and 'metadata' not in file:
            fusion_files.append(os.path.join(fusion_dir, file))
    
    fusion_files.sort()
    print(f"找到 {len(fusion_files)} 个融合数据文件")
    
    # 处理每个文件
    output_files = []
    for csv_file in fusion_files:
        try:
            output_file = process_fusion_file(csv_file, method=method, radius=radius, max_nn=max_nn)
            output_files.append(output_file)
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {e}")
            continue
    
    print(f"\n{'='*60}")
    print(f"批量处理完成!")
    print(f"成功处理 {len(output_files)} 个文件")
    print(f"输出文件:")
    for output_file in output_files:
        print(f"  - {output_file}")
    print(f"{'='*60}")
    
    return output_files

if __name__ == "__main__":
    # 示例用法
    
    # 处理单个文件
    # csv_file = "fusion_data/hsi_point_fusion_35.csv"
    # output_file = process_fusion_file(csv_file, method='open3d', radius=5.0, max_nn=30)
    
    # 批量处理所有文件
    output_files = process_all_fusion_files(method='open3d', radius=5.0, max_nn=30)
