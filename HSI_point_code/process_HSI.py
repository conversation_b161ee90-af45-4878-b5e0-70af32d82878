import re
import os
import numpy as np
import scipy
import scipy.io as sio
import spectral
from osgeo import gdal_array
import matplotlib.pyplot as plt
import shutil
import tempfile
from typing import Tuple, Optional, Union, List, Dict, Any

from utils import read_ply, Downsampling_HSI

# -*- coding: utf-8 -*-
# 定义常量
# 文件路径常量
HSI_DATA_DIR = r'/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/data_apple_hsi'
HSI_FILE_NAME = 'Test_70add0_2025-05-23_07-13-20'
HDR_PATH = os.path.join(HSI_DATA_DIR, HSI_FILE_NAME, 'capture', f'REFLECTANCE_{HSI_FILE_NAME}.hdr')
DAT_PATH = os.path.join(HSI_DATA_DIR, HSI_FILE_NAME, 'capture', f'REFLECTANCE_{HSI_FILE_NAME}.dat')

# 波长相关参数
START_WAVELENGTH = 400  # 起始波长(nm)
END_WAVELENGTH = 1000   # 结束波长(nm)
TOTAL_BANDS = 224       # 总波段数
TARGET_WAVELENGTH = 600 # 目标显示波长(nm)


"""
裁剪参数
35: 
CROP_START_Y, CROP_END_Y = 114, 669  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 120, 832  # X轴裁剪范围

40:
CROP_START_Y, CROP_END_Y = 135, 690  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 117, 830  # X轴裁剪范围

55:
CROP_START_Y, CROP_END_Y = 131, 686  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 127, 844  # X轴裁剪范围

60
CROP_START_Y, CROP_END_Y = 140, 699  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 126, 849  # X轴裁剪范围

65
CROP_START_Y, CROP_END_Y = 137, 696  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 135, 852  # X轴裁剪范围
"""
# 裁剪参数
CROP_START_Y, CROP_END_Y = 131, 696  # Y轴裁剪范围
CROP_START_X, CROP_END_X = 149, 866  # X轴裁剪范围

# 降采样目标尺寸
TARGET_SIZE = (86, 79, 224)  # (高度, 宽度, 波段数)

# 背景移除阈值
BACKGROUND_THRESHOLD = 0.1  # 低于此阈值的光谱强度被视为背景


def get_band_index(wavelength: float, 
                 start_nm: float = START_WAVELENGTH, 
                 end_nm: float = END_WAVELENGTH, 
                 total_bands: int = TOTAL_BANDS) -> int:
    """
    计算指定波长对应的波段索引
    
    参数:
        wavelength: float - 目标波长(nm)
        start_nm: float - 起始波长(nm)
        end_nm: float - 结束波长(nm)
        total_bands: int - 总波段数
        
    返回:
        int - 对应的波段索引
    """
    step = (end_nm - start_nm) / (total_bands - 1)
    index = int((wavelength - start_nm) / step)
    return min(max(0, index), total_bands - 1)


def apply_depth_to_hsi(ply_file: str, hsi_image_resized: np.ndarray) -> np.ndarray:
    """
    根据深度数据阈值处理高光谱图像
    
    参数:
        ply_file: str - PLY点云文件路径
        hsi_image_resized: np.ndarray - 需要处理的高光谱图像数据
        
    返回:
        np.ndarray - 处理后的高光谱图像数据
    """
    points = read_ply(ply_file)
    depth = np.array(points[:, 2]).reshape(hsi_image_resized.shape[0], hsi_image_resized.shape[1])

    # 遍历深度矩阵，将深度为 0 的位置对应的光谱设置为 0
    for i in range(depth.shape[0]):
        for j in range(depth.shape[1]):
            if depth[i, j] == 0:
                hsi_image_resized[i, j, :] = 0  # 将该位置的光谱设置为全零
    
    print("根据深度数据处理了高光谱图像")
    return hsi_image_resized


def load_hsi_data(hdr_path: str, dat_path: str) -> Optional[np.ndarray]:
    """
    加载高光谱数据并处理编码问题
    
    参数:
        hdr_path: str - HDR文件路径
        dat_path: str - DAT文件路径
    
    返回:
        Optional[np.ndarray] - 加载的高光谱图像数据，如果加载失败则返回None
    """
    try:
        # 创建临时文件来存储修复后的HDR内容
        temp_dir = tempfile.mkdtemp()
        temp_hdr_path = os.path.join(temp_dir, os.path.basename(hdr_path))
        
        # 以二进制模式读取原始HDR文件
        with open(hdr_path, 'rb') as f:
            hdr_content = f.read()
            
        # 尝试使用不同编码解码内容
        encodings = ['latin1', 'cp1252', 'iso-8859-1']
        decoded_content = None
        
        for encoding in encodings:
            try:
                decoded_content = hdr_content.decode(encoding)
                print(f"成功使用 {encoding} 编码解码HDR文件")
                break
            except UnicodeDecodeError:
                continue
        
        if decoded_content is None:
            print("无法解码HDR文件，尝试直接加载")
            return spectral.envi.open(hdr_path, dat_path)
        
        # 将解码后的内容写入临时文件
        with open(temp_hdr_path, 'w', encoding='utf-8') as f:
            f.write(decoded_content)
        
        # 使用临时HDR文件加载高光谱数据
        img = spectral.envi.open(temp_hdr_path, dat_path)
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        return img
    except Exception as e:
        print(f"加载高光谱数据出错: {e}")
        return None


def plot_hyperspectral_band(hyperspectral_image: np.ndarray, 
                          band_index: int, 
                          title_prefix: str = "") -> None:
    """
    绘制高光谱图像在特定波长的可视化图像
    
    参数:
        hyperspectral_image: np.ndarray - 3D高光谱数据数组 (高度, 宽度, 波段数)
        band_index: int - 要绘制的波段索引
        title_prefix: str - 标题前缀
        
    返回:
        None
    """
    plt.figure(figsize=(10, 8), dpi=200)
    band_image = hyperspectral_image[:, :, band_index]
    plt.imshow(band_image, cmap='jet')
    plt.colorbar(label='光谱强度')
    
    # 计算实际波长
    wavelength = START_WAVELENGTH + (band_index * (END_WAVELENGTH - START_WAVELENGTH) / TOTAL_BANDS)
    
    # 构建标题
    if title_prefix:
        plt.title(f'{title_prefix} - {wavelength:.0f}nm波段图像')
    else:
        plt.title(f'高光谱图像 {wavelength:.0f}nm波段')
        
    plt.xlabel('宽度 (像素)')
    plt.ylabel('高度 (像素)')
    plt.axis('on')
    plt.show()


def remove_background(hsi_image: np.ndarray, threshold: float = BACKGROUND_THRESHOLD) -> np.ndarray:
    """
    去除高光谱图像背景
    
    参数:
        hsi_image: np.ndarray - 高光谱图像数据
        threshold: float - 光谱强度阈值，低于此值视为背景
    
    返回:
        np.ndarray - 去除背景后的高光谱图像
    """
    # 计算每个像素点所有波段的平均强度
    mean_intensity = np.mean(hsi_image, axis=2)
    
    # 创建背景掩码
    mask = mean_intensity > threshold
    
    # 扩展掩码维度以匹配原始数据
    mask = np.expand_dims(mask, axis=2)
    mask = np.repeat(mask, hsi_image.shape[2], axis=2)
    
    # 应用掩码，将背景设为0
    hsi_image_no_bg = np.where(mask, hsi_image, 0)
    print(f"去除高光谱图像背景 (阈值: {threshold})")
    
    return hsi_image_no_bg


def save_hsi_image(image: np.ndarray, 
                  step_name: str, 
                  band_index: int, 
                  save_dir: str) -> str:
    """
    保存高光谱图像的某个波段为图片文件
    
    参数:
        image: np.ndarray - 高光谱图像数据
        step_name: str - 处理步骤名称，用于文件名
        band_index: int - 要保存的波段索引
        save_dir: str - 保存目录路径
    
    返回:
        str - 保存的文件路径
    """
    plt.figure(figsize=(10, 8), dpi=200)
    band_image = image[:, :, band_index]
    plt.imshow(band_image, cmap='jet')
    plt.colorbar(label='光谱强度')
    
    # 计算实际波长
    wavelength = START_WAVELENGTH + (band_index * (END_WAVELENGTH - START_WAVELENGTH) / TOTAL_BANDS)
    
    plt.title(f'高光谱图像 {wavelength:.0f}nm - {step_name}')
    plt.xlabel('宽度 (像素)')
    plt.ylabel('高度 (像素)')
    plt.axis('on')
    
    # 构建保存路径
    save_path = os.path.join(save_dir, f'{step_name}_band_{wavelength:.0f}nm.png')
    plt.savefig(save_path)
    plt.close()
    
    print(f"保存图像: {save_path}")
    return save_path


def extract_file_number(file_path: str) -> str:
    """
    从文件路径中提取编号
    例如从"REFLECTANCE_Test_35add0_2025-05-23_07-58-33"中提取"35"
    
    参数:
        file_path: str - 文件路径
        
    返回:
        str - 提取的编号，如果未找到则返回"unknown"
    """
    file_name = os.path.basename(file_path).split('.')[0]  # 获取不带扩展名的文件名
    match = re.search(r'Test_(\d+)add', file_name)  # 使用正则表达式匹配"Test_"后面、"add"前面的数字
    return match.group(1) if match else "unknown"  # 提取匹配的数字，如果没有匹配则设为"unknown"


def process_hyperspectral_image() -> None:
    """
    处理高光谱图像的主函数
    包括加载、裁剪、旋转、翻转、降采样、去除背景和保存
    """
    print("----- 开始处理高光谱图像 -----")
    
    # 尝试加载高光谱数据
    try:
        print(f"尝试直接加载高光谱数据: {HDR_PATH}")
        img = spectral.envi.open(HDR_PATH, DAT_PATH)
        print("成功直接加载高光谱数据")
    except Exception as e:
        print(f"直接加载高光谱数据失败: {e}")
        print("尝试使用自定义函数加载...")
        img = load_hsi_data(HDR_PATH, DAT_PATH)

    if img is None:
        print("无法加载高光谱数据，程序退出")
        return
    
    # 提取处理的高光谱数据编号和设置输出目录
    file_number = extract_file_number(HDR_PATH)
    print(f"提取的文件编号: {file_number}")
    
    # 确定输出目录
    data_dir = os.path.join(HSI_DATA_DIR, f'{file_number}_pre_hsi_data')
    process_dir = os.path.join(data_dir, 'process_hsi')
    
    # 创建输出目录
    for directory in [data_dir, process_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")
    
    # 输出高光谱数据信息
    print(f"高光谱图像数据的形状: {img.shape}")
    
    # 计算目标波长对应的波段索引
    band_index = get_band_index(TARGET_WAVELENGTH)
    print(f"{TARGET_WAVELENGTH}nm对应的波段索引为: {band_index}")
    
    # 显示原始高光谱图像
    plot_hyperspectral_band(img, band_index, "原始图像")
    
    # 裁剪图像
    print(f"裁剪图像范围: Y({CROP_START_Y}:{CROP_END_Y}), X({CROP_START_X}:{CROP_END_X})")
    cropped_image = img[CROP_START_Y:CROP_END_Y, CROP_START_X:CROP_END_X, :]
    plot_hyperspectral_band(cropped_image, band_index, "裁剪后")
    
    # 保存处理过程中的图像
    print("保存处理过程中的图像...")
    
    # 保存原始图像
    save_hsi_image(img, 'original', band_index, process_dir)
    
    # 保存裁剪后的图像
    cropped_image = img[CROP_START_Y:CROP_END_Y, CROP_START_X:CROP_END_X, :]
    save_hsi_image(cropped_image, 'cropped', band_index, process_dir)
    
    # 逆时针旋转90度
    print("逆时针旋转图像90度...")
    rotated_image = np.rot90(cropped_image, k=1, axes=(0, 1))
    save_hsi_image(rotated_image, 'rotated', band_index, process_dir)
    
    # 垂直镜面翻转（上下翻转）
    print("执行垂直镜面翻转...")
    flipped_image = np.flipud(rotated_image)
    save_hsi_image(flipped_image, 'flipped', band_index, process_dir)
    
    # 降采样
    print(f"将图像降采样至目标尺寸: {TARGET_SIZE}...")
    resized_image = Downsampling_HSI(flipped_image, TARGET_SIZE)
    save_hsi_image(resized_image, 'resized', band_index, process_dir)
    
    # 去除背景
    processed_image = remove_background(resized_image, threshold=BACKGROUND_THRESHOLD)
    save_hsi_image(processed_image, 'no_background', band_index, process_dir)
    
    # 保存处理后的数据
    mat_file = os.path.join(data_dir, 'resized_HSI.mat')
    print(f"保存MATLAB格式文件: {mat_file}")
    scipy.io.savemat(mat_file, {"HSI": processed_image})
    
    tif_file = os.path.join(data_dir, 'resized_HSI.tif')
    print(f"保存TIFF格式文件: {tif_file}")
    gdal_array.SaveArray(processed_image.transpose(2, 0, 1), tif_file, format="GTiff")
    
    print("----- 高光谱图像处理完成 -----") 


if __name__ == "__main__":
    process_hyperspectral_image()