import numpy as np
import scipy.io
from osgeo import gdal_array
from dataset import visualize_data
from utils import *

def visualize_3D_grayscale(HSI_resized, resized_points_2D, band_index=110):
    gray_image_resized = HSI_resized[:, :, band_index]
    print("gray_image_resized.shape",gray_image_resized.shape)

    mask = resized_points_2D[:, 2] != 0
    x = resized_points_2D[mask, 0]
    y = resized_points_2D[mask, 1]
    z = resized_points_2D[mask, 2]
    # x = resized_points_2D[:, 0]
    # y = resized_points_2D[:, 1]
    # z = resized_points_2D[:, 2]

    # 翻转 Z 轴方向
    z = -z

    # 确保 z 从 0 开始
    z_min = z.min()
    z = z - z_min

    # 创建 3D 图形
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 绘制点云的三维散点图
    # 绘制深度不为 0 的点云的三维散点图
    scatter = ax.scatter(x, y, z + 25, c=z, cmap='viridis', marker='o', s=10, label="Point Cloud")

    # 在 z=0 平面上绘制灰度图
    X, Y = np.meshgrid(np.linspace(x.min(), x.max(), 88), np.linspace(y.min(), y.max(), 105))
    # 归一化到 [0, 1]
    gray_image_normalized = (gray_image_resized - gray_image_resized.min()) / (
                gray_image_resized.max() - gray_image_resized.min())

    # 转换到 [0, 255] 范围
    gray_image_scaled = (gray_image_normalized * 255).astype(np.uint8)
    ax.plot_surface(X, Y, np.zeros_like(X), rstride=1, cstride=1, facecolors=plt.cm.gray(gray_image_scaled),
                    shade=False)

    # 设置标签和标题
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z(deepth)")
    ax.set_title("HSI and CloudPoint")
    plt.show()


ply_file = r'/Users/<USER>/PycharmProjects/spect-wyx/1231雷达真实数据/data/raw_241111/apple-3/DepthPoints_385266.ply'  # 替换为你的 PLY 文件路径
save_file = r"data/241111/3_apple/resized_cloudpoint.ply"
# ply_file = r'/Users/<USER>/PycharmProjects/spect-wyx/1231雷达真实数据/data/0107/第一个/348650/DepthPoints_348650.ply'  # 替换为你的 PLY 文件路径
# save_file = r"data/0107/第一个/272967/resized_cloudpoint.ply"
# 读取点云数据
points = read_ply(ply_file)
print("LOAD original point cloud...")

# 绘制三视图（XY、XZ、YZ）
# plot_orthographic_views(points, title="Cropped Orthographic Views")
# visualize_point_cloud(points)

# 定义裁剪范围
# x_limits = (-40, 33)
# y_limits = (-42, 32)
# z_limits = (300, 450)

"""
2_aplle
x_limits = (-45, 39)
y_limits = (-46, 39.5)
z_limits = (300, 850) 


1_apple
x_limits = (-50, 32.5)
y_limits = (-50, 40)
z_limits = (300, 850)


3_apple
x_limits = (-44, 36)
y_limits = (-44, 44)
z_limits = (300, 850)

"""

# 上下
# x_limits = (-40, 40)
# y_limits = (-38, 50)
# z_limits = (300, 850)

x_limits = (-44, 36)
y_limits = (-44, 44)
z_limits = (300, 850)

# 裁剪点云数据
cropped_points = crop_point_cloud(points, x_limits, y_limits, z_limits)
# 点云数据沿 x 轴翻转
cropped_points[:,1] = -cropped_points[:,1]
# 绘制剪裁后的三视图（XY、XZ、YZ）
plot_orthographic_views(cropped_points, title="Cropped Orthographic Views")
# 显示点云的数量
print(f"裁剪后点云数量: {len(cropped_points):,}")

# 下采样并填充没有数据的点为0
# x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(cropped_points, grid_size=(108, 89), x_range=(-40, 0), y_range=(-30, 20))
x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(cropped_points, grid_size=(108, 89), x_range=(-40, 0), y_range=(-30, 20))
# 可视化降采样后的点云数据
plot_3d_surface(x_grid, y_grid, z_grid)
# 绘制降采样后的三视图（XY、XZ、YZ）
plot_orthographic_views(downsampled_points, title="Cropped Orthographic Views")

# 去除不符合深度范围深度
modified_downsampled_points = apply_depth_threshold(downsampled_points, depth_threshold=480)
# 绘制三维图
# plot_3d_points(modified_downsampled_points, title="Cropped 3D View")
# 绘制三视图（XY、XZ、YZ）
# plot_orthographic_views(modified_downsampled_points, title="Cropped Orthographic Views")

#------ 结合高光谱图像看深度图的的大小是否正确
hsi_image_resized = scipy.io.loadmat("data/241111/3_apple/resized_HSI.mat")["HSI"]
# hsi_image_resized = scipy.io.loadmat("data/0107/第一个/resized_HSI.mat")["HSI"]
plot_hyperspectral_band(hsi_image_resized, 30)
hsi_image_resized1 = apply_depth_to_hsi(modified_downsampled_points, hsi_image_resized)
# plot_hyperspectral_band(hsi_image_resized1, 30)
plot_hyperspectral_band_overlay(hsi_image_resized, hsi_image_resized1, band_index=30)


#------- 对高光谱数据进行上下镜面翻转
hsi_image_resized = np.flipud(hsi_image_resized)
hsi_image_resized1 = np.flipud(hsi_image_resized1)

# 绘制翻转后的高光谱图像对比
plot_hyperspectral_band_overlay(hsi_image_resized, hsi_image_resized1, band_index=30)
print(hsi_image_resized1.shape)

#------- 对点云数据进行镜面翻转
modified_downsampled_points[:,0] = -modified_downsampled_points[:,0]

visualize_3D_grayscale(hsi_image_resized1, modified_downsampled_points, band_index=30)



