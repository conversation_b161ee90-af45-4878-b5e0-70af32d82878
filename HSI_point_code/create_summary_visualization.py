#!/usr/bin/env python3
"""
创建法向量修正效果的汇总可视化
展示多个融合数据文件的修正统计和效果对比
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob

def analyze_all_corrections():
    """
    分析所有融合数据文件的法向量修正效果
    """
    # 查找所有原始和修正后的文件对
    original_files = glob.glob("../fusion_data/*_with_normals.csv")
    original_files = [f for f in original_files if '_oriented' not in f]
    
    results = []
    
    for original_file in sorted(original_files):
        # 构建对应的修正文件路径
        oriented_file = original_file.replace('_with_normals.csv', '_with_normals_oriented.csv')
        
        if not os.path.exists(oriented_file):
            continue
            
        # 提取文件基础名称
        base_name = os.path.basename(original_file).replace('_with_normals.csv', '')
        
        print(f"分析文件: {base_name}")
        
        # 加载数据
        df_original = pd.read_csv(original_file)
        df_oriented = pd.read_csv(oriented_file)
        
        points = df_original[['x', 'y', 'z']].values
        normals_original = df_original[['normal_x', 'normal_y', 'normal_z']].values
        normals_oriented = df_oriented[['normal_x', 'normal_y', 'normal_z']].values
        
        # 计算修正统计
        dot_products = np.sum(normals_original * normals_oriented, axis=1)
        flipped_mask = dot_products < 0
        flipped_count = np.sum(flipped_mask)
        
        # 分析空间分布
        center = np.mean(points, axis=0)
        distances_to_center = np.linalg.norm(points - center, axis=1)
        max_distance = np.max(distances_to_center)
        edge_threshold = 0.8 * max_distance
        edge_mask = distances_to_center > edge_threshold
        
        edge_flipped = np.sum(flipped_mask & edge_mask)
        center_flipped = np.sum(flipped_mask & ~edge_mask)
        edge_total = np.sum(edge_mask)
        center_total = np.sum(~edge_mask)
        
        # 计算Z分量统计
        z_original_min = normals_original[:, 2].min()
        z_original_max = normals_original[:, 2].max()
        z_oriented_min = normals_oriented[:, 2].min()
        z_oriented_max = normals_oriented[:, 2].max()
        
        negative_z_original = np.sum(normals_original[:, 2] < 0)
        negative_z_oriented = np.sum(normals_oriented[:, 2] < 0)
        
        results.append({
            'file': base_name,
            'total_points': len(points),
            'flipped_count': flipped_count,
            'flipped_ratio': flipped_count / len(points) * 100,
            'edge_flipped': edge_flipped,
            'edge_total': edge_total,
            'edge_ratio': edge_flipped / max(edge_total, 1) * 100,
            'center_flipped': center_flipped,
            'center_total': center_total,
            'center_ratio': center_flipped / max(center_total, 1) * 100,
            'z_original_min': z_original_min,
            'z_original_max': z_original_max,
            'z_oriented_min': z_oriented_min,
            'z_oriented_max': z_oriented_max,
            'negative_z_original': negative_z_original,
            'negative_z_oriented': negative_z_oriented
        })
    
    return results

def create_summary_visualization(results):
    """
    创建汇总可视化图表
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    files = [r['file'] for r in results]
    
    # 1. 总体修正比例
    ax1 = axes[0, 0]
    flipped_ratios = [r['flipped_ratio'] for r in results]
    bars1 = ax1.bar(range(len(files)), flipped_ratios, color='skyblue', alpha=0.7)
    ax1.set_title('Overall Correction Ratio by File', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Correction Ratio (%)')
    ax1.set_xticks(range(len(files)))
    ax1.set_xticklabels([f.replace('hsi_point_fusion_', '') for f in files], rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, ratio) in enumerate(zip(bars1, flipped_ratios)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{ratio:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 2. 边缘vs中心区域修正比例对比
    ax2 = axes[0, 1]
    edge_ratios = [r['edge_ratio'] for r in results]
    center_ratios = [r['center_ratio'] for r in results]
    
    x = np.arange(len(files))
    width = 0.35
    
    bars2a = ax2.bar(x - width/2, edge_ratios, width, label='Edge Region', color='orange', alpha=0.7)
    bars2b = ax2.bar(x + width/2, center_ratios, width, label='Center Region', color='green', alpha=0.7)
    
    ax2.set_title('Correction Ratio: Edge vs Center', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Correction Ratio (%)')
    ax2.set_xticks(x)
    ax2.set_xticklabels([f.replace('hsi_point_fusion_', '') for f in files], rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Z分量范围改善
    ax3 = axes[0, 2]
    z_original_mins = [r['z_original_min'] for r in results]
    z_oriented_mins = [r['z_oriented_min'] for r in results]
    
    bars3a = ax3.bar(x - width/2, z_original_mins, width, label='Original Min Z', color='red', alpha=0.7)
    bars3b = ax3.bar(x + width/2, z_oriented_mins, width, label='Corrected Min Z', color='blue', alpha=0.7)
    
    ax3.set_title('Z-component Range Improvement', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Min Z-component')
    ax3.set_xticks(x)
    ax3.set_xticklabels([f.replace('hsi_point_fusion_', '') for f in files], rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # 4. 负Z分量点数消除
    ax4 = axes[1, 0]
    negative_original = [r['negative_z_original'] for r in results]
    negative_oriented = [r['negative_z_oriented'] for r in results]
    
    bars4a = ax4.bar(x - width/2, negative_original, width, label='Original', color='red', alpha=0.7)
    bars4b = ax4.bar(x + width/2, negative_oriented, width, label='Corrected', color='green', alpha=0.7)
    
    ax4.set_title('Negative Z-component Points Elimination', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Number of Points')
    ax4.set_xticks(x)
    ax4.set_xticklabels([f.replace('hsi_point_fusion_', '') for f in files], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 修正点数统计
    ax5 = axes[1, 1]
    total_points = [r['total_points'] for r in results]
    flipped_counts = [r['flipped_count'] for r in results]
    
    bars5a = ax5.bar(range(len(files)), total_points, color='lightgray', alpha=0.7, label='Total Points')
    bars5b = ax5.bar(range(len(files)), flipped_counts, color='red', alpha=0.8, label='Corrected Points')
    
    ax5.set_title('Point Count Statistics', fontsize=12, fontweight='bold')
    ax5.set_ylabel('Number of Points')
    ax5.set_xticks(range(len(files)))
    ax5.set_xticklabels([f.replace('hsi_point_fusion_', '') for f in files], rotation=45)
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 汇总统计表格
    ax6 = axes[1, 2]
    ax6.axis('tight')
    ax6.axis('off')
    
    # 创建统计表格
    table_data = []
    headers = ['File', 'Total', 'Corrected', 'Ratio(%)', 'Edge(%)', 'Center(%)']
    
    for r in results:
        row = [
            r['file'].replace('hsi_point_fusion_', ''),
            f"{r['total_points']:,}",
            f"{r['flipped_count']:,}",
            f"{r['flipped_ratio']:.1f}",
            f"{r['edge_ratio']:.1f}",
            f"{r['center_ratio']:.1f}"
        ]
        table_data.append(row)
    
    table = ax6.table(cellText=table_data, colLabels=headers, 
                     cellLoc='center', loc='center',
                     colWidths=[0.15, 0.15, 0.15, 0.15, 0.15, 0.15])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 1.5)
    
    # 设置表格样式
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax6.set_title('Summary Statistics Table', fontsize=12, fontweight='bold', pad=20)
    
    plt.tight_layout()
    
    # 保存图表
    save_path = "../fusion_data/normal_correction_summary.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"汇总可视化已保存到: {save_path}")
    
    return save_path

def print_summary_statistics(results):
    """
    打印汇总统计信息
    """
    print("\n" + "="*80)
    print("法向量修正效果汇总统计")
    print("="*80)
    
    total_points_all = sum(r['total_points'] for r in results)
    total_corrected_all = sum(r['flipped_count'] for r in results)
    
    print(f"处理文件数量: {len(results)}")
    print(f"总点数: {total_points_all:,}")
    print(f"总修正点数: {total_corrected_all:,}")
    print(f"总体修正比例: {total_corrected_all/total_points_all*100:.2f}%")
    
    print(f"\n各文件修正比例:")
    for r in results:
        print(f"  {r['file']}: {r['flipped_ratio']:.2f}% ({r['flipped_count']}/{r['total_points']})")
    
    print(f"\n边缘区域修正比例 (平均): {np.mean([r['edge_ratio'] for r in results]):.2f}%")
    print(f"中心区域修正比例 (平均): {np.mean([r['center_ratio'] for r in results]):.2f}%")
    
    print(f"\n负Z分量消除效果:")
    total_negative_original = sum(r['negative_z_original'] for r in results)
    total_negative_oriented = sum(r['negative_z_oriented'] for r in results)
    print(f"  修正前负Z分量点数: {total_negative_original:,}")
    print(f"  修正后负Z分量点数: {total_negative_oriented:,}")
    print(f"  消除比例: {(total_negative_original-total_negative_oriented)/max(total_negative_original,1)*100:.2f}%")

if __name__ == "__main__":
    print("开始分析所有融合数据文件的法向量修正效果...")
    
    # 分析所有文件
    results = analyze_all_corrections()
    
    if not results:
        print("未找到可分析的文件对！")
        exit(1)
    
    # 打印统计信息
    print_summary_statistics(results)
    
    # 创建可视化
    save_path = create_summary_visualization(results)
    
    print(f"\n汇总分析完成！")
    print(f"可视化结果保存在: {save_path}")
