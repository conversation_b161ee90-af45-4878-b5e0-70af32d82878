#!/usr/bin/env python3
"""
可视化法向量修正前后的对比效果
展示原始法向量和方向一致性处理后的法向量差异
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

def load_comparison_data(original_file, oriented_file):
    """
    加载原始和修正后的法向量数据
    
    参数:
        original_file (str): 原始法向量文件路径
        oriented_file (str): 修正后法向量文件路径
        
    返回:
        tuple: (points, normals_original, normals_oriented)
    """
    print(f"加载原始数据: {original_file}")
    print(f"加载修正数据: {oriented_file}")
    
    # 加载原始数据
    df_original = pd.read_csv(original_file)
    points = df_original[['x', 'y', 'z']].values
    normals_original = df_original[['normal_x', 'normal_y', 'normal_z']].values
    
    # 加载修正后数据
    df_oriented = pd.read_csv(oriented_file)
    normals_oriented = df_oriented[['normal_x', 'normal_y', 'normal_z']].values
    
    print(f"数据点数量: {len(points)}")
    print(f"原始法向量Z分量范围: [{normals_original[:, 2].min():.3f}, {normals_original[:, 2].max():.3f}]")
    print(f"修正法向量Z分量范围: [{normals_oriented[:, 2].min():.3f}, {normals_oriented[:, 2].max():.3f}]")
    
    return points, normals_original, normals_oriented

def analyze_normal_differences(points, normals_original, normals_oriented):
    """
    分析法向量修正前后的差异
    """
    # 计算点积来判断方向差异
    dot_products = np.sum(normals_original * normals_oriented, axis=1)
    
    # 找出方向相反的点（点积为负）
    flipped_mask = dot_products < 0
    flipped_count = np.sum(flipped_mask)
    
    # 计算角度差异
    angles = np.degrees(np.arccos(np.clip(np.abs(dot_products), 0, 1)))
    
    # 分析空间分布
    center = np.mean(points, axis=0)
    distances_to_center = np.linalg.norm(points - center, axis=1)
    max_distance = np.max(distances_to_center)
    
    # 定义边缘区域（距离中心超过80%最大距离）
    edge_threshold = 0.8 * max_distance
    edge_mask = distances_to_center > edge_threshold
    
    # 统计边缘和中心区域的修正情况
    edge_flipped = np.sum(flipped_mask & edge_mask)
    center_flipped = np.sum(flipped_mask & ~edge_mask)
    
    print(f"\n=== 法向量修正分析 ===")
    print(f"总点数: {len(points)}")
    print(f"方向修正的点数: {flipped_count} ({flipped_count/len(points)*100:.2f}%)")
    print(f"边缘区域修正: {edge_flipped}/{np.sum(edge_mask)} ({edge_flipped/max(np.sum(edge_mask), 1)*100:.2f}%)")
    print(f"中心区域修正: {center_flipped}/{np.sum(~edge_mask)} ({center_flipped/max(np.sum(~edge_mask), 1)*100:.2f}%)")
    print(f"平均角度差异: {np.mean(angles):.2f}°")
    print(f"最大角度差异: {np.max(angles):.2f}°")
    
    return flipped_mask, edge_mask, angles

def create_comparison_visualization(points, normals_original, normals_oriented, 
                                  flipped_mask, edge_mask, save_path=None):
    """
    创建法向量修正前后的对比可视化
    """
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 3D点云和法向量对比
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    ax1.scatter(points[:, 0], points[:, 1], points[:, 2], 
               c=normals_original[:, 2], cmap='RdYlBu', s=2, alpha=0.6)
    ax1.set_title('Original Normals (Z-component colored)', fontsize=12)
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')

    ax2 = fig.add_subplot(2, 3, 2, projection='3d')
    ax2.scatter(points[:, 0], points[:, 1], points[:, 2],
               c=normals_oriented[:, 2], cmap='RdYlBu', s=2, alpha=0.6)
    ax2.set_title('Corrected Normals (Z-component colored)', fontsize=12)
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')

    # 2. 修正点的空间分布
    ax3 = fig.add_subplot(2, 3, 3, projection='3d')
    # 未修正的点（灰色）
    unflipped_points = points[~flipped_mask]
    ax3.scatter(unflipped_points[:, 0], unflipped_points[:, 1], unflipped_points[:, 2],
               c='lightgray', s=1, alpha=0.3, label='Unchanged')
    # 修正的点（红色）
    flipped_points = points[flipped_mask]
    ax3.scatter(flipped_points[:, 0], flipped_points[:, 1], flipped_points[:, 2],
               c='red', s=3, alpha=0.8, label='Corrected')
    ax3.set_title('Spatial Distribution of Corrected Points', fontsize=12)
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    ax3.legend()

    # 3. Z分量分布对比
    ax4 = fig.add_subplot(2, 3, 4)
    ax4.hist(normals_original[:, 2], bins=50, alpha=0.7, label='Original', color='blue', density=True)
    ax4.hist(normals_oriented[:, 2], bins=50, alpha=0.7, label='Corrected', color='red', density=True)
    ax4.axvline(x=0, color='black', linestyle='--', alpha=0.5, label='Z=0')
    ax4.set_xlabel('Normal Z-component')
    ax4.set_ylabel('Density')
    ax4.set_title('Normal Z-component Distribution Comparison', fontsize=12)
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 4. 边缘vs中心区域修正情况
    ax5 = fig.add_subplot(2, 3, 5)
    edge_flipped = np.sum(flipped_mask & edge_mask)
    center_flipped = np.sum(flipped_mask & ~edge_mask)
    edge_total = np.sum(edge_mask)
    center_total = np.sum(~edge_mask)

    categories = ['Edge Region', 'Center Region']
    flipped_counts = [edge_flipped, center_flipped]
    total_counts = [edge_total, center_total]
    percentages = [f/t*100 for f, t in zip(flipped_counts, total_counts)]

    bars = ax5.bar(categories, percentages, color=['orange', 'green'], alpha=0.7)
    ax5.set_ylabel('Correction Ratio (%)')
    ax5.set_title('Correction Ratio by Region', fontsize=12)
    ax5.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar, count, total, pct in zip(bars, flipped_counts, total_counts, percentages):
        height = bar.get_height()
        ax5.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{count}/{total}\n({pct:.1f}%)', 
                ha='center', va='bottom', fontsize=10)
    
    # 5. 法向量方向箭头可视化（采样显示）
    ax6 = fig.add_subplot(2, 3, 6, projection='3d')
    
    # 采样显示（避免过于密集）
    sample_indices = np.random.choice(len(points), size=min(500, len(points)), replace=False)
    sample_points = points[sample_indices]
    sample_normals_orig = normals_original[sample_indices]
    sample_normals_oriented = normals_oriented[sample_indices]
    sample_flipped = flipped_mask[sample_indices]
    
    # 显示点云
    ax6.scatter(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2], 
               c='lightblue', s=10, alpha=0.6)
    
    # 显示修正前的法向量（蓝色）
    scale = 2.0
    ax6.quiver(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2],
              sample_normals_orig[:, 0]*scale, sample_normals_orig[:, 1]*scale, sample_normals_orig[:, 2]*scale,
              color='blue', alpha=0.5, length=1, normalize=False, label='Original Normals')
    
    # 显示修正后的法向量（红色，仅显示被修正的点）
    flipped_sample_points = sample_points[sample_flipped]
    flipped_sample_normals = sample_normals_oriented[sample_flipped]
    if len(flipped_sample_points) > 0:
        ax6.quiver(flipped_sample_points[:, 0], flipped_sample_points[:, 1], flipped_sample_points[:, 2],
                  flipped_sample_normals[:, 0]*scale, flipped_sample_normals[:, 1]*scale, flipped_sample_normals[:, 2]*scale,
                  color='red', alpha=0.8, length=1, normalize=False, label='Corrected Normals')

    ax6.set_title('Normal Direction Comparison (Sampled)', fontsize=12)
    ax6.set_xlabel('X')
    ax6.set_ylabel('Y')
    ax6.set_zlabel('Z')
    ax6.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
    else:
        plt.show()

def visualize_normal_correction(fusion_file_base_name):
    """
    可视化指定融合数据文件的法向量修正效果
    
    参数:
        fusion_file_base_name (str): 融合数据文件基础名称，如 "hsi_point_fusion_40"
    """
    # 构建文件路径
    original_file = f"../fusion_data/{fusion_file_base_name}_with_normals.csv"
    oriented_file = f"../fusion_data/{fusion_file_base_name}_with_normals_oriented.csv"
    
    # 检查文件是否存在
    if not os.path.exists(original_file):
        print(f"错误: 原始文件不存在 - {original_file}")
        return
    
    if not os.path.exists(oriented_file):
        print(f"错误: 修正文件不存在 - {oriented_file}")
        return
    
    print(f"开始可视化法向量修正效果: {fusion_file_base_name}")
    
    # 加载数据
    points, normals_original, normals_oriented = load_comparison_data(original_file, oriented_file)
    
    # 分析差异
    flipped_mask, edge_mask, angles = analyze_normal_differences(points, normals_original, normals_oriented)
    
    # 创建可视化
    save_path = f"../fusion_data/{fusion_file_base_name}_normal_correction_comparison.png"
    create_comparison_visualization(points, normals_original, normals_oriented, 
                                  flipped_mask, edge_mask, save_path)
    
    return save_path

if __name__ == "__main__":
    # 可视化 hsi_point_fusion_40 的法向量修正效果
    fusion_file = "hsi_point_fusion_40"
    
    print("=" * 80)
    print("法向量修正效果可视化")
    print("=" * 80)
    
    result_path = visualize_normal_correction(fusion_file)
    
    print(f"\n可视化完成！")
    print(f"结果保存在: {result_path}")
    print("\n可视化内容包括:")
    print("1. 原始vs修正后的3D法向量对比")
    print("2. 修正点的空间分布")
    print("3. Z分量分布直方图对比")
    print("4. 边缘vs中心区域修正统计")
    print("5. 法向量方向箭头对比")
