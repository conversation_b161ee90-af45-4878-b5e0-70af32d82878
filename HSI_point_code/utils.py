from scipy.ndimage import zoom
import scipy
import scipy.io as sio
import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import torch
from sklearn.decomposition import PCA
from scipy.spatial import KDTree
from scipy import signal
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import pandas as pd
from matplotlib import rcParams
import platform
import plotly.graph_objects as go

def set_plot_style():
    """
    根据操作系统自动设置合适的中文字体
    """
    system = platform.system()
    
    if system == 'Darwin':  # macOS
        # macOS 常用中文字体优先级
        chinese_fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti', 'PingFang HK', 'Hiragino Sans GB']
    elif system == 'Windows':
        # Windows 常用中文字体优先级
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux 或其他系统
        # Linux 常用中文字体优先级
        chinese_fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK JP', 'Noto Sans CJK TC']

    # 检查系统中是否存在这些字体
    from matplotlib.font_manager import FontManager
    fm = FontManager()
    system_fonts = set(f.name for f in fm.ttflist)
    
    # 选择第一个可用的字体
    for font in chinese_fonts:
        if font in system_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            break
    else:
        print("警告：未找到合适的中文字体，可能会导致中文显示异常")
    
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 在定义完函数后调用
set_plot_style()

# 读取 PLY 文件并将其转换为 NumPy 数组
def read_ply(file_path):
    point_cloud = o3d.io.read_point_cloud(file_path)
    points = np.asarray(point_cloud.points)
    return points

def visualize_point_cloud(points):
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # 设置视图参数
    vis = o3d.visualization.Visualizer()
    vis.create_window()
    vis.add_geometry(pcd)
    
    # 设置默认视角
    ctr = vis.get_view_control()
    ctr.set_zoom(0.8)
    ctr.set_front([0, 0, -1])
    ctr.set_up([0, 1, 0])
    
    vis.run()
    vis.destroy_window()

# HSI和target_size大小要一致
def Downsampling_HSI(HSI, target_size):
    H0, W0, B0 = HSI.shape[0], HSI.shape[1], HSI.shape[2]
    # 确保高光谱图像和点云图的大小一致
    if (H0 * W0) > target_size[0] * target_size[1]:
        # 计算下采样因子
        downsample_factor = (target_size[0] / HSI.shape[0],
                             target_size[1] / HSI.shape[1],
                             target_size[2] / HSI.shape[2])

        # 对高光谱图像进行空间分辨率的降采样
        HSI_resized = zoom(HSI, downsample_factor)

        return HSI_resized

## 可视化3D和灰度图
def visualize_3D_grayscale(HSI_resized, resized_points_2D, band_index=110):
    gray_image_resized = HSI_resized[:, :, band_index]

    mask = resized_points_2D[:, 2] != 0
    x = resized_points_2D[mask, 0]
    y = resized_points_2D[mask, 1]
    z = resized_points_2D[mask, 2]
    # x = resized_points_2D[:, 0]
    # y = resized_points_2D[:, 1]
    # z = resized_points_2D[:, 2]

    # 翻转 Z 轴方向
    z = -z

    # 确保 z 从 0 开始
    z_min = z.min()
    z = z - z_min

    # 创建 3D 图形
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 绘制点云的三维散点图
    # 绘制深度不为 0 的点云的三维散点图
    scatter = ax.scatter(x, y, z + 25, c=z, cmap='viridis', marker='o', s=10, label="Point Cloud")

    # 在 z=0 平面上绘制灰度图
    X, Y = np.meshgrid(np.linspace(x.min(), x.max(), 88), np.linspace(y.min(), y.max(), 105))
    # 归一化到 [0, 1]
    gray_image_normalized = (gray_image_resized - gray_image_resized.min()) / (
                gray_image_resized.max() - gray_image_resized.min())

    # 转换到 [0, 255] 范围
    gray_image_scaled = (gray_image_normalized * 255).astype(np.uint8)
    ax.plot_surface(X, Y, np.zeros_like(X), rstride=1, cstride=1, facecolors=plt.cm.gray(gray_image_scaled),
                    shade=False)

    # 设置标签和标题
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z(deepth)")
    ax.set_title("HSI and CloudPoint")
    plt.show()

def SNV(data):
    """
        :param data: raw spectrum data, shape (n_samples, n_features)
       :return: data after SNV :(n_samples, n_features)
    """
    m = data.shape[0]
    n = data.shape[1]
    print(m, n)  #
    # 求标准差
    data_std = np.std(data, axis=1)  # 每条光谱的标准差
    # 求平均值
    data_average = np.mean(data, axis=1)  # 每条光谱的平均值
    # SNV计算
    data_snv = [[((data[i][j] - data_average[i]) / data_std[i]) for j in range(n)] for i in range(m)]
    return data_snv

def SG(data, w=11, p=2):
    """
       :param data: raw spectrum data, shape (n_samples, n_features)
       :param w: int
       :param p: int
       :return: data after SG :(n_samples, n_features)
    """
    return signal.savgol_filter(data, w, p)

# 在给定半径计算点云密度
def compute_density(points, radius=0.1):
    # 创建 Open3D 点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    # 使用 Open3D 的 KDTree
    pcd_tree = o3d.geometry.KDTreeFlann(pcd)

    # 统计每个点在给定半径内的邻域点数
    densities = []
    for point in points:
        [_, idx, _] = pcd_tree.search_radius_vector_3d(point, radius)
        densities.append(len(idx) - 1)  # 减去自己本身的点

    return densities

def compute_normals_with_adjusted_radius(points, radius=0.2, max_nn=30):
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    # o3d.geometry.estimate_normals(pcd, search_param=o3d.geometry.KDTreeSearchParamKNN(knn=20))
    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn))
    normals = np.asarray(pcd.normals)
    return normals

def compute_normals_manually(points, radius=0.1):
    """使用 PCA 手动计算每个点的法向量。"""
    # 创建 KDTree 以便快速找到邻域点
    tree = KDTree(points)
    normals = []

    for point in points:
        # 查找每个点的邻域点
        indices = tree.query_ball_point(point, radius)
        neighbors = points[indices]

        # 如果邻域点不足 3 个，跳过该点
        if len(neighbors) < 3:
            normals.append([0, 0, 1])  # 或者可以跳过
            continue

        # 使用 PCA 拟合平面
        pca = PCA(n_components=3)
        pca.fit(neighbors)
        normal = pca.components_[-1]  # 最小特征值对应的方向

        # 确保法向量朝向 z 轴正方向
        if normal[2] < 0:
            normal = -normal
        normals.append(normal)

    # 转换为 numpy 数组
    normals = np.array(normals)
    return normals

def compute_inclination_orientation_inclination(normals):
    # 计算倾斜角度和取向
    angles = np.degrees(np.arccos(np.clip(normals[:, 2], -1.0, 1.0)))
    # angles = np.degrees(np.arccos(normals[:, 2]))  # 计算与z轴的倾斜角度
    orientations = np.arctan2(normals[:, 1], normals[:, 0])  # 计算XY平面上的取向
    # 计算法向量的倾斜角，与 z 轴的夹角
    inclination = np.degrees(np.arctan2(normals[:, 2], np.sqrt(normals[:, 0] ** 2 + normals[:, 1] ** 2)))  # 倾斜角转换为角度
    return angles, orientations, inclination

def calculate_inclination_and_direction(normals):
    # 计算法向量在 XY 平面的方向角
    angles = np.degrees(np.arctan2(normals[:, 1], normals[:, 0]))  # 方向角转换为角度
    # 计算法向量的倾斜角，与 z 轴的夹角
    inclination = np.degrees(np.arctan2(normals[:, 2], np.sqrt(normals[:, 0]**2 + normals[:, 1]**2)))  # 倾斜角转换为角度
    return angles, inclination

def visualize_normals(points, normals):
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.normals = o3d.utility.Vector3dVector(normals)
    o3d.visualization.draw_geometries([pcd], point_show_normal=True)

def calculate_change_rate_with_depth(HSI_resized_SNV, spectrum_GT, depth_map):
    """
    计算光谱变化率，并确保在深度为 0 的位置变化率为 0。

    参数:
        HSI_resized_SNV (ndarray): 观测光谱数据，形状为 (105, 88, 273)。
        spectrum_GT (ndarray): 真实光谱数据，形状为 (105, 88, 273)。
        depth_map (ndarray): 深度数据，形状为 (105, 88)。

    返回:
        change_rate (ndarray): 光谱变化率，形状为 (105, 88, 273)。
    """
    # 初始化变化率矩阵
    change_rate = np.zeros_like(HSI_resized_SNV)

    # 防止除以 0，加一个极小数
    epsilon = 1e-8

    # 遍历所有波段，计算变化率
    for i in range(HSI_resized_SNV.shape[2]):
        # 在深度不为 0 的位置计算变化率
        valid_mask = depth_map != 0  # 深度不为 0 的位置
        change_rate[:, :, i][valid_mask] = (HSI_resized_SNV[:, :, i][valid_mask] - spectrum_GT[:, :, i][valid_mask]) / (spectrum_GT[:, :, i][valid_mask] + epsilon)

    # 返回变化率矩阵
    return change_rate


# 计算光谱角距离
def calculate_spectral_angle_distance(HSI_resized_SNV, spectrum_GT, depth_map):
    """
    计算光谱角距离，并确保在深度为 0 的位置角距离为 0。

    参数:
        HSI_resized_SNV (ndarray): 观测光谱数据，形状为 (105, 88, 273)。
        spectrum_GT (ndarray): 真实光谱数据，形状为 (105, 88, 273)。
        depth_map (ndarray): 深度数据，形状为 (105, 88)。

    返回:
        SAD (ndarray): 光谱角距离矩阵，形状为 (105, 88)。
    """
    # 初始化 SAD 矩阵为 0
    SAD = np.zeros((HSI_resized_SNV.shape[0], HSI_resized_SNV.shape[1]))

    # 防止除以 0，加一个极小数
    epsilon = 1e-8

    # 在深度不为 0 的位置计算 SAD
    valid_mask = depth_map != 0  # 深度不为 0 的位置
    for i in range(HSI_resized_SNV.shape[0]):
        for j in range(HSI_resized_SNV.shape[1]):
            if valid_mask[i, j]:
                # 计算两个光谱向量的点积
                dot_product = np.dot(HSI_resized_SNV[i, j, :], spectrum_GT[i, j, :])
                # 计算两个光谱向量的模
                norm1 = np.linalg.norm(HSI_resized_SNV[i, j, :])
                norm2 = np.linalg.norm(spectrum_GT[i, j, :])
                # 计算光谱角距离
                SAD[i, j] = np.arccos(dot_product / (norm1 * norm2 + epsilon))

    return SAD

def weights_init(m):
    if isinstance(m, torch.nn.Linear):
        torch.nn.init.xavier_uniform_(m.weight)
        if m.bias is not None:
            torch.nn.init.zeros_(m.bias)

# 裁剪点云数据
def crop_point_cloud(points, x_limits=None, y_limits=None, z_limits=None):
    # 使用给定的范围裁剪 X、Y、Z 轴上的点
    x_min, x_max = x_limits if x_limits else (np.min(points[:, 0]), np.max(points[:, 0]))
    y_min, y_max = y_limits if y_limits else (np.min(points[:, 1]), np.max(points[:, 1]))
    z_min, z_max = z_limits if z_limits else (np.min(points[:, 2]), np.max(points[:, 2]))

    # 条件筛选：根据 X、Y、Z 范围裁剪点云
    mask = (points[:, 0] >= x_min) & (points[:, 0] <= x_max) & \
           (points[:, 1] >= y_min) & (points[:, 1] <= y_max) & \
           (points[:, 2] >= z_min) & (points[:, 2] <= z_max)

    cropped_points = points[mask]
    return cropped_points

def plot_images_with_axes(original_image, cropped_image, band_index):
    """
    绘制带有横纵坐标的原始图像和裁剪后的图像。

    :param original_image: 原始高光谱图像
    :param cropped_image: 裁剪后的高光谱图像
    :param band_index: 用于显示的波段索引
    """
    original_band_image = original_image[:, :, band_index]
    cropped_band_image = cropped_image[:, :, band_index]

    plt.figure(figsize=(12, 6))

    # 原始图像
    plt.subplot(1, 2, 1)
    plt.imshow(original_band_image, cmap='gray')
    plt.title('original HSI(band {0})'.format(band_index + 1))
    plt.colorbar(label='zValue')
    plt.xlabel('xWeight')
    plt.ylabel('yLength')

    # 裁剪后的图像
    plt.subplot(1, 2, 2)
    plt.imshow(cropped_band_image, cmap='gray')
    plt.title('crop HSI(band {0})'.format(band_index + 1))
    plt.colorbar(label='zValue')
    plt.xlabel('xWeight')
    plt.ylabel('yLength')

    plt.tight_layout()
    plt.show()


def calculate_center_spectrum(hyperspectral_data):
    center_region = hyperspectral_data[41:51, 51:61, :]  # 取中心区域（10x10）
    center_spectrum = np.mean(center_region, axis=(0, 1))  # 计算每个波段的平均值
    return center_spectrum


# 将点云降采样到给定网格大小，并用0填充空白区域
def downsample_point_cloud(points, grid_size=(105, 88)):
    # 提取点云的x, y, z坐标
    x = points[:, 0]
    y = points[:, 1]
    z = points[:, 2]

    # 获取x, y的最小最大值，确定网格范围
    x_min, x_max = np.min(x), np.max(x)
    y_min, y_max = np.min(y), np.max(y)

    # 创建目标网格的x, y坐标
    x_grid = np.linspace(x_min, x_max, grid_size[0])
    y_grid = np.linspace(y_min, y_max, grid_size[1])

    # 创建网格的空白z矩阵，初始化为0
    z_grid = np.zeros((grid_size[0], grid_size[1]))

    # 将点云映射到网格位置并填充z值
    for i in range(len(x)):
        # 找到当前点在网格中的位置
        x_idx = np.searchsorted(x_grid, x[i]) - 1
        y_idx = np.searchsorted(y_grid, y[i]) - 1

        # 确保索引在有效范围内
        x_idx = np.clip(x_idx, 0, grid_size[0] - 1)
        y_idx = np.clip(y_idx, 0, grid_size[1] - 1)

        # 填充z值
        z_grid[x_idx, y_idx] = z[i]

    # 将其转化为点云格式
    sampled_points = []
    for i in range(grid_size[0]):
        for j in range(grid_size[1]):

            sampled_points.append([x_grid[i], y_grid[j], z_grid[i, j]])

    return x_grid, y_grid, z_grid, np.array(sampled_points)

# 绘制三维图
def plot_3d_points(points, title="3D View"):
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    sc = ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=points[:, 2], cmap='viridis')
    fig.colorbar(sc, ax=ax, label='Z Value')
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(title)
    ax.invert_zaxis()
    # 删除网格线
    ax.grid(False)
    plt.show()

# 可视化降采样后的点云
def plot_3d_surface(x_grid, y_grid, z_grid):
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D

    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 这里要用 np.meshgrid 来创建匹配形状的 x 和 y 网格
    X, Y = np.meshgrid(x_grid, y_grid)

    # 绘制 3D 表面
    ax.plot_surface(X, Y, z_grid.T, cmap='viridis')  # 注意这里 z_grid 需要转置

    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    # 删除网格线
    ax.grid(False)
    plt.show()
# 保存点云数据
def save_point_cloud_open3d(points, filename="point_cloud.ply"):
    """
    使用 open3d 保存点云数据为 .ply 文件

    参数:
        points (ndarray): 点云数据，形状为 (N, 3)，每行包含一个点的 x, y, z 坐标
        filename (str): 保存的文件名
    """
    # 创建一个点云对象
    pcd = o3d.geometry.PointCloud()
    # 设置点云数据
    pcd.points = o3d.utility.Vector3dVector(points)
    # 保存点云数据为 .ply 文件
    o3d.io.write_point_cloud(filename, pcd)

#  H W C
def plot_hyperspectral_band(hyperspectral_image, band_index):
    """
    绘制特定波段的高光谱图像。

    :param hyperspectral_image: 三维 NumPy 数组 (height, width, num_bands)
    :param band_index: 要绘制的波段索引
    """
    plt.figure(figsize=(8, 6), dpi=200)  # 增加 DPI 设置
    band_image = hyperspectral_image[:, :, band_index]
    plt.imshow(band_image, cmap='jet')
    plt.colorbar(label='光谱强度')
    plt.title(f'波段 {band_index + 1} 图像')
    plt.axis('on')
    plt.show()

# 绘制三视图（XY、XZ、YZ）
def plot_orthographic_views(points, title=None, save_path=None):
    """
    Plot orthographic views of point cloud
    
    Parameters:
    points: Point cloud coordinates (N, 3)
    title: Title for the plot (optional)
    save_path: Path to save the visualization (optional)
    """
    fig = plt.figure(figsize=(15, 5))
    
    # Top view
    ax1 = fig.add_subplot(131)
    scatter1 = ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter1, ax=ax1, label='Z')
    ax1.set_title('Top View (XY)')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.grid(False)  # 删除网格线
    ax1.axis('equal')
    
    # Front view
    ax2 = fig.add_subplot(132)
    scatter2 = ax2.scatter(points[:, 0], points[:, 2], c=points[:, 1], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter2, ax=ax2, label='Y')
    ax2.set_title('Front View (XZ)')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Z')
    ax2.grid(False)  # 删除网格线
    
    # Side view
    ax3 = fig.add_subplot(133)
    scatter3 = ax3.scatter(points[:, 1], points[:, 2], c=points[:, 0], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter3, ax=ax3, label='X')
    ax3.set_title('Side View (YZ)')
    ax3.set_xlabel('Y')
    ax3.set_ylabel('Z')
    ax3.grid(False)  # 删除网格线
    
    if title:
        fig.suptitle(title)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    else:
        plt.show()

# 点云数据下采样，同时将范围 x_range=(-20, 0), y_range=(0, 10)的深度为0位置取周围的深度填充
def downsample_and_fill(points, grid_size=(105, 88), x_range=(-20, 0), y_range=(0, 10)):
    x, y, z = points[:, 0], points[:, 1], points[:, 2]

    # 创建目标网格的x, y坐标
    x_min, x_max = np.min(x), np.max(x)
    y_min, y_max = np.min(y), np.max(y)

    x_grid = np.linspace(x_min, x_max, grid_size[0])
    y_grid = np.linspace(y_min, y_max, grid_size[1])

    # 初始化 z_grid，尺寸为 (105, 88)，并填充为 0
    z_grid = np.zeros((grid_size[0], grid_size[1]))
    # 初始化 z_grid，尺寸为 (105, 88)，并填充为 500
    # z_grid = np.full((grid_size[0], grid_size[1]), 420)

    # 将点云数据映射到网格上
    for i in range(len(points)):
        x_idx = np.searchsorted(x_grid, x[i]) - 1
        y_idx = np.searchsorted(y_grid, y[i]) - 1

        # 确保索引在有效范围内
        x_idx = np.clip(x_idx, 0, grid_size[0] - 1)
        y_idx = np.clip(y_idx, 0, grid_size[1] - 1)

        # 填充 z 值
        z_grid[x_idx, y_idx] = z[i]

    # 识别出给定 x 范围和 y 范围中的缺失点
    x_mask = (x_grid >= x_range[0]) & (x_grid <= x_range[1])
    y_mask = (y_grid >= y_range[0]) & (y_grid <= y_range[1])
    center_mask = np.outer(x_mask, y_mask)
    missing_points = (z_grid == 0) & center_mask

    # 遍历缺失点并用左右的非空点进行填充
    for i in range(1, grid_size[0] - 1):
        for j in range(1, grid_size[1] - 1):
            if missing_points[i, j]:  # 如果是缺失点
                # 获取左右的非空值
                left_value = z_grid[i, j - 1] if z_grid[i, j - 1] != 0 else None
                right_value = z_grid[i, j + 1] if z_grid[i, j + 1] != 0 else None

                # 计算填充值，取非空值的平均
                values = [v for v in [left_value, right_value] if v is not None]
                if values:
                    z_grid[i, j] = np.mean(values)

    # 将 z_grid 转换为 (105*88, 3) 的点云格式
    sampled_points = []
    for i in range(grid_size[0]):
        for j in range(grid_size[1]):
            sampled_points.append([x_grid[i], y_grid[j], z_grid[i, j]])

    return x_grid, y_grid, z_grid, np.array(sampled_points)

# 根据深度数据的阈值处理高光谱图像
def apply_depth_to_hsi(points, hsi_image_resized):
    """
    根据点云深度数据处理高光谱图像
    
    参数:
    points: 点云数据，形状为(N, 3)
    hsi_image_resized: 高光谱图像数据，形状为(H, W, B)
    
    返回:
    处理后的高光谱图像
    """
    # 将点云深度信息重塑为与高光谱图像相同的空间维度
    depth = np.array(points[:, 2]).reshape(hsi_image_resized.shape[0], hsi_image_resized.shape[1])
    hsi_image_resized1 = hsi_image_resized.copy()
    
    # 遍历深度矩阵，将深度为 0 的位置对应的光谱设置为 0
    for i in range(depth.shape[0]):
        for j in range(depth.shape[1]):
            if depth[i, j] == 0:
                hsi_image_resized1[i, j, :] = 0  # 将该位置的光谱设置为全零
    
    return hsi_image_resized1


# 叠加显示处理前和处理后的高光谱图像在指定波段的灰度图
def plot_hyperspectral_band_overlay(original_hsi, processed_hsi, band_index):
    """


    参数:
        original_hsi (ndarray): 处理前的高光谱图像数据，形状为 (H, W, B)。
        processed_hsi (ndarray): 处理后的高光谱图像数据，形状为 (H, W, B)。
        band_index (int): 要显示的波段索引。
    """
    # 提取指定波段的图像
    original_band = original_hsi[:, :, band_index]
    processed_band = processed_hsi[:, :, band_index]

    # 创建叠加显示
    plt.figure(figsize=(8, 6))
    plt.imshow(original_band, cmap='jet', label='Original', vmin=0, vmax=8000)  # 原图为灰色，半透明
    plt.imshow(processed_band, cmap='gray', alpha=0.5, label='Processed', vmin=0, vmax=8000)  # 处理后为彩色，半透明

    plt.colorbar(label="Intensity")
    plt.title(f"Hyperspectral Band {band_index} - Overlay of Original and Processed")
    plt.axis('on')
    plt.legend(['Original', 'Processed'], loc='upper right')
    plt.show()

# 将点云数据中深度大于阈值的位置设置为0。
def apply_depth_threshold(points, depth_threshold=450):
    """


    参数:
        points (ndarray): 点云数据，形状为 (num_points, 3)，每行包含 (x, y, z) 坐标。
        depth_threshold (float): 深度阈值，超过此值的点深度将被设置为0。

    返回:
        ndarray: 修改后的点云数据，深度大于阈值的位置被设置为0。
    """
    # 复制点云数据，以免修改原数据
    modified_points = np.copy(points)

    # 将深度（z 值）大于阈值的位置设置为0
    modified_points[modified_points[:, 2] > depth_threshold, 2] = 0

    return modified_points

# 旋转矩阵（逆时针旋转90°）
def rotate_point_cloud_90_degrees(points):
    rotation_matrix = np.array([
        [0, -1, 0],
        [1, 0, 0],
        [0, 0, 1]
    ])
    rotated_points = np.dot(points, rotation_matrix.T)  # 应用旋转矩阵
    return rotated_points
