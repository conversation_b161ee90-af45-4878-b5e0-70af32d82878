import os
import numpy as np
import scipy.io
from matplotlib import pyplot as plt
from osgeo import gdal_array
from typing import Tuple, List, Optional, Union, Dict, Any
import h5py
import pandas as pd

from utils import (
    apply_depth_threshold, apply_depth_to_hsi, crop_point_cloud, 
    downsample_and_fill, plot_3d_points, plot_3d_surface, 
    plot_hyperspectral_band_overlay, plot_orthographic_views, 
    read_ply, save_point_cloud_open3d, visualize_point_cloud
)
# 定义常量
PLY_FILE_PATH = r'/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/cloud_point_apple_data/250989/DepthPoints_250989.ply'
SAVE_FILE_PATH = r"/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/cloud_point_apple_data/250989/resized_cloudpoint.ply"
SAVE_ORTHOGRAPHIC_PATH = "/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/cloud_point_apple_data/250989/orthographic_views.png"
import re

PRE_HSI_DATA_NAME = "70"

HSI_FILE_PATH = f"/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/data_apple_hsi/{PRE_HSI_DATA_NAME}_pre_hsi_data/resized_HSI.mat"

# 使用正则表达式从HSI_FILE_PATH中提取数字“35”作为FINAL_NAME
match = re.search(r'/(\d+)_pre_hsi_data/', HSI_FILE_PATH)
if match:
    FINAL_NAME = match.group(1)
else:
    FINAL_NAME = ""

# 融合数据保存路径
FUSION_OUTPUT_DIR = "/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/fusion_data"
FUSION_OUTPUT_FILE = os.path.join(FUSION_OUTPUT_DIR, f"hsi_point_fusion_{FINAL_NAME}.h5")
FUSION_OUTPUT_CSV = os.path.join(FUSION_OUTPUT_DIR, f"hsi_point_fusion_{FINAL_NAME}.csv")
FUSION_META_CSV = os.path.join(FUSION_OUTPUT_DIR, f"hsi_point_fusion_{FINAL_NAME}_metadata.csv")

# 点云数据裁剪参数
X_LIMITS = (-12, 77)  # X轴范围
Y_LIMITS = (-36, 55) # Y轴范围
Z_LIMITS = (300, 850) # Z轴范围（深度）

# 降采样网格尺寸
GRID_SIZE = (86, 79)
X_RANGE = (0, 0)
Y_RANGE = (0, 0)

# 深度阈值
DEPTH_THRESHOLD = 500

# 波长参数
START_WAVELENGTH = 400  # 起始波长(nm)
END_WAVELENGTH = 1000   # 结束波长(nm)
TOTAL_BANDS = 224       # 总波段数
TARGET_WAVELENGTH = 700 # 用于可视化的目标波长(nm)


def plot_orthographic_views(points: np.ndarray, 
                          title: Optional[str] = None, 
                          save_path: Optional[str] = None) -> None:
    """
    绘制点云的三个正交视图（俯视图XY、前视图XZ、侧视图YZ）
    
    参数:
        points: np.ndarray - 点云坐标数组，形状为(N, 3)
        title: Optional[str] - 图表标题（可选）
        save_path: Optional[str] - 图像保存路径（可选）
    
    返回:
        None
    """
    fig = plt.figure(figsize=(15, 5))
    
    # 设置透明背景
    fig.patch.set_alpha(0)
    
    # 绘制俯视图(XY平面)
    ax1 = fig.add_subplot(131)
    scatter1 = ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter1, ax=ax1, label='Z轴深度')
    ax1.set_title('俯视图 (XY平面)')
    ax1.set_xlabel('X轴')
    ax1.set_ylabel('Y轴')
    ax1.grid(False)  # 移除网格线
    ax1.axis('equal')
    ax1.patch.set_alpha(0)  # 设置子图背景透明
    
    # 绘制前视图(XZ平面)
    ax2 = fig.add_subplot(132)
    scatter2 = ax2.scatter(points[:, 0], points[:, 2], c=points[:, 1], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter2, ax=ax2, label='Y轴位置')
    ax2.set_title('前视图 (XZ平面)')
    ax2.set_xlabel('X轴')
    ax2.set_ylabel('Z轴深度')
    ax2.grid(False)  # 移除网格线
    ax2.patch.set_alpha(0)  # 设置子图背景透明
    
    # 绘制侧视图(YZ平面)
    ax3 = fig.add_subplot(133)
    scatter3 = ax3.scatter(points[:, 1], points[:, 2], c=points[:, 0], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter3, ax=ax3, label='X轴位置')
    ax3.set_title('侧视图 (YZ平面)')
    ax3.set_xlabel('Y轴')
    ax3.set_ylabel('Z轴深度')
    ax3.grid(False)  # 移除网格线
    ax3.patch.set_alpha(0)  # 设置子图背景透明
    
    if title:
        fig.suptitle(title)
    
    plt.tight_layout()
    
    # 保存图片或显示图片
    if save_path:
        plt.savefig(save_path, 
                   dpi=300, 
                   bbox_inches='tight',
                   transparent=True,  # 保存为透明背景
                   format='png')      # 指定保存格式为PNG
        plt.close()
    else:
        plt.show()
        
        
def plot_3d_points(points: np.ndarray, title: str = "3D俯视图") -> None:
    """
    绘制点云的三维俯视图
    
    参数:
        points: np.ndarray - 点云坐标数组，形状为(N, 3)
        title: str - 图表标题
    
    返回:
        None
    """
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    sc = ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                   c=points[:, 2], cmap='viridis', s=2)
    fig.colorbar(sc, ax=ax, label='Z轴深度值')
    ax.set_xlabel('X轴')
    ax.set_ylabel('Y轴')
    
    # 隐藏z轴刻度，保留轴标签
    ax.set_zticks([])  # 移除z轴刻度
    ax.zaxis.line.set_visible(False)  # 隐藏z轴线
    
    # 设置观察角度为俯视图
    ax.view_init(elev=90, azim=-90)
    
    ax.set_title(title)
    ax.invert_zaxis()  # 反转Z轴方向，使其符合常规深度表示
    plt.show()
        
        
def plot_hyperspectral_band_overlay(original_hsi: np.ndarray, 
                                  processed_hsi: np.ndarray, 
                                  band_index: int,
                                  wavelength_nm: Optional[float] = None) -> None:
    """
    叠加显示原始和处理后的高光谱图像在指定波段的灰度图
    
    参数:
        original_hsi: np.ndarray - 原始高光谱数据，形状为(H, W, B)
        processed_hsi: np.ndarray - 处理后的高光谱数据，形状为(H, W, B)
        band_index: int - 要显示的波段索引
        wavelength_nm: Optional[float] - 波长值(nm)，如果不提供则计算
    
    返回:
        None
    """
    # 提取指定波段的图像
    original_band = original_hsi[:, :, band_index]
    processed_band = processed_hsi[:, :, band_index]

    # 计算实际波长（如果未提供）
    if wavelength_nm is None:
        wavelength_nm = 400 + (band_index * (1000-400)/224)

    # 创建叠加显示图像
    plt.figure(figsize=(10, 8))
    
    # 使用不同的色图显示原始和处理后的图像
    plt.imshow(original_band, cmap='jet', vmin=0, vmax=8000)
    plt.imshow(processed_band, cmap='gray', alpha=0.5, vmin=0, vmax=8000)

    plt.colorbar(label="光谱强度")
    plt.title(f"高光谱图像 {wavelength_nm:.0f}nm - 原始 vs 处理后")
    plt.xlabel('宽度 (像素)')
    plt.ylabel('高度 (像素)')
    plt.axis('on')
    plt.legend(['原始', '处理后'], loc='upper right')
    plt.show()


def get_band_index(wavelength: float, 
                 start_nm: float = START_WAVELENGTH, 
                 end_nm: float = END_WAVELENGTH, 
                 total_bands: int = TOTAL_BANDS) -> int:
    """
    计算指定波长对应的波段索引
    
    参数:
        wavelength: float - 目标波长(nm)
        start_nm: float - 起始波长(nm)
        end_nm: float - 结束波长(nm)
        total_bands: int - 总波段数
    
    返回:
        int - 对应的波段索引
    """
    step = (end_nm - start_nm) / (total_bands - 1)
    index = int((wavelength - start_nm) / step)
    return min(max(0, index), total_bands - 1)


def create_fusion_data(point_cloud: np.ndarray, 
                     hsi_data: np.ndarray) -> np.ndarray:
    """
    创建点云和高光谱数据的融合数据集
    
    参数:
        point_cloud: np.ndarray - 点云数据，形状为(N, 3)或(H, W, 3)
        hsi_data: np.ndarray - 高光谱数据，形状为(H, W, B)
    
    返回:
        np.ndarray - 融合后的数据，形状为(H, W, 3+B)，其中前3个通道是XYZ坐标，后B个通道是光谱数据
    """
    print("开始创建融合数据...")
    
    # 检查数据形状
    height, width, bands = hsi_data.shape
    print(f"高光谱数据形状: {hsi_data.shape}")
    
    # 如果点云是1D数组(N,3)，转换为2D图像格式(H,W,3)
    if len(point_cloud.shape) == 2:
        print("将点云数据从(N,3)重塑为(H,W,3)...")
        point_cloud_2d = np.zeros((height, width, 3))
        # 假设点云数据是按行优先排列的
        for i in range(point_cloud.shape[0]):
            row = i // width
            col = i % width
            if row < height and col < width:
                point_cloud_2d[row, col, :] = point_cloud[i, :]
        point_cloud = point_cloud_2d
    
    print(f"点云数据形状: {point_cloud.shape}")
    
    # 确保点云和高光谱数据具有相同的空间尺寸
    if point_cloud.shape[0] != height or point_cloud.shape[1] != width:
        raise ValueError(f"点云数据尺寸({point_cloud.shape[0]}x{point_cloud.shape[1]})与高光谱数据尺寸({height}x{width})不匹配")
    
    # 创建融合数据结构
    fusion_data = np.zeros((height, width, 3 + bands))
    
    # 填充XYZ坐标
    fusion_data[:, :, 0:3] = point_cloud
    
    # 填充光谱数据
    fusion_data[:, :, 3:] = hsi_data
    
    print(f"融合数据创建完成，形状: {fusion_data.shape}")
    return fusion_data


def reshape_point_cloud_to_image(point_cloud: np.ndarray, height: int, width: int) -> np.ndarray:
    """
    将一维点云数据重塑为二维图像格式
    
    参数:
        point_cloud: np.ndarray - 点云数据，形状为(N, 3)
        height: int - 目标图像高度
        width: int - 目标图像宽度
    
    返回:
        np.ndarray - 重塑后的点云数据，形状为(height, width, 3)
    """
    if point_cloud.shape[0] != height * width:
        print(f"警告：点云点数({point_cloud.shape[0]})与目标图像像素数({height * width})不匹配")
    
    # 创建一个空的3D数组
    point_cloud_2d = np.zeros((height, width, 3))
    
    # 重塑点云数据
    count = min(point_cloud.shape[0], height * width)
    for i in range(count):
        row = i // width
        col = i % width
        point_cloud_2d[row, col, :] = point_cloud[i, :]
    
    return point_cloud_2d


def save_fusion_data(fusion_data: np.ndarray, 
                   file_path: str, 
                   wavelengths: Optional[np.ndarray] = None) -> None:
    """
    保存融合数据到HDF5文件
    
    参数:
        fusion_data: np.ndarray - 融合数据，形状为(H, W, 3+B)
        file_path: str - 保存路径
        wavelengths: Optional[np.ndarray] - 波长数组，如果不提供则根据START_WAVELENGTH和END_WAVELENGTH计算
    
    返回:
        None
    """
    # 创建目录（如果不存在）
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 计算波长数组（如果未提供）
    if wavelengths is None:
        bands = fusion_data.shape[2] - 3
        wavelengths = np.linspace(START_WAVELENGTH, END_WAVELENGTH, bands)
    
    # 保存到HDF5文件
    with h5py.File(file_path, 'w') as f:
        # 创建数据集
        f.create_dataset('fusion_data', data=fusion_data)
        f.create_dataset('wavelengths', data=wavelengths)
        
        # 添加元数据
        f.attrs['height'] = fusion_data.shape[0]
        f.attrs['width'] = fusion_data.shape[1]
        f.attrs['bands'] = fusion_data.shape[2] - 3
        f.attrs['has_xyz'] = True
        f.attrs['xyz_channels'] = 3
        f.attrs['start_wavelength'] = START_WAVELENGTH
        f.attrs['end_wavelength'] = END_WAVELENGTH
        f.attrs['description'] = '融合的点云和高光谱数据，前3个通道为XYZ坐标，后续通道为光谱数据'
    
    print(f"融合数据已保存到: {file_path}")


def load_fusion_data(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    从HDF5文件加载融合数据
    
    参数:
        file_path: str - 文件路径
    
    返回:
        Tuple[np.ndarray, np.ndarray] - (融合数据, 波长数组)
    """
    with h5py.File(file_path, 'r') as f:
        fusion_data = f['fusion_data'][:]
        wavelengths = f['wavelengths'][:]
        
        print(f"加载融合数据，形状: {fusion_data.shape}")
        print(f"波长范围: {wavelengths[0]:.1f}nm - {wavelengths[-1]:.1f}nm")
        
        return fusion_data, wavelengths


def visualize_fusion_data(fusion_data: np.ndarray, 
                        band_index: int = None,
                        wavelength: float = None) -> None:
    """
    可视化融合数据
    
    参数:
        fusion_data: np.ndarray - 融合数据，形状为(H, W, 3+B)
        band_index: int - 要显示的波段索引，如果未提供但提供了wavelength，则自动计算
        wavelength: float - 要显示的波长(nm)，如果未提供则使用默认波段
    
    返回:
        None
    """
    height, width, channels = fusion_data.shape
    
    # 提取点云数据和高光谱数据
    point_cloud = fusion_data[:, :, 0:3]
    hsi_data = fusion_data[:, :, 3:]
    
    # 计算波段索引（如果提供了波长）
    if band_index is None:
        if wavelength is not None:
            band_index = get_band_index(wavelength)
        else:
            band_index = get_band_index(TARGET_WAVELENGTH)
    
    # 1. 叠加显示深度图和高光谱图像
    plt.figure(figsize=(16, 8))
    
    # 获取深度图和光谱图
    depth_map = point_cloud[:, :, 2]  # Z坐标作为深度
    spectral_band = hsi_data[:, :, band_index] if band_index < hsi_data.shape[2] else None
    wavelength_value = START_WAVELENGTH + (band_index * (END_WAVELENGTH - START_WAVELENGTH) / hsi_data.shape[2])
    
    # 叠加显示
    plt.imshow(depth_map, cmap='viridis', interpolation='nearest')
    if spectral_band is not None:
        plt.imshow(spectral_band, cmap='jet', alpha=0.5, interpolation='nearest')  # 设置透明度为50%
    
    plt.colorbar(label='深度值/光谱强度')
    plt.title(f'点云深度图与高光谱图像({wavelength_value:.1f}nm)叠加显示')
    plt.xlabel('X坐标 (像素)')
    plt.ylabel('Y坐标 (像素)')
    plt.tight_layout()
    plt.show()
    
    # 2. 分开显示深度图和高光谱图像（保留原有功能）
    fig = plt.figure(figsize=(16, 8))
    
    # 显示点云深度图
    ax1 = fig.add_subplot(121)
    im1 = ax1.imshow(depth_map, cmap='viridis')
    plt.colorbar(im1, ax=ax1, label='深度值')
    ax1.set_title('点云深度图')
    ax1.set_xlabel('X坐标')
    ax1.set_ylabel('Y坐标')
    
    # 显示对应波段的高光谱图像
    ax2 = fig.add_subplot(122)
    if spectral_band is not None:
        im2 = ax2.imshow(spectral_band, cmap='jet')
        plt.colorbar(im2, ax=ax2, label='光谱强度')
        ax2.set_title(f'高光谱图像 ({wavelength_value:.1f}nm)')
    else:
        ax2.text(0.5, 0.5, '无效波段索引', ha='center', va='center')
    
    ax2.set_xlabel('X坐标')
    ax2.set_ylabel('Y坐标')
    
    plt.tight_layout()
    plt.show()
    
    # 3. 3D可视化带有高光谱信息的点云
    visualize_point_cloud_with_spectrum(point_cloud.reshape(-1, 3), 
                                      hsi_data.reshape(-1, hsi_data.shape[2]), 
                                      band_index)


def visualize_point_cloud_with_spectrum(points: np.ndarray, 
                                     spectrum: np.ndarray, 
                                     band_index: int) -> None:
    """
    使用高光谱数据作为颜色信息可视化点云
    
    参数:
        points: np.ndarray - 点云坐标，形状为(N, 3)
        spectrum: np.ndarray - 对应的光谱数据，形状为(N, B)
        band_index: int - 要用于着色的波段索引
    
    返回:
        None
    """
    # 确保点数匹配
    if points.shape[0] != spectrum.shape[0]:
        raise ValueError(f"点数不匹配: 点云({points.shape[0]})与光谱数据({spectrum.shape[0]})")
    
    # 提取指定波段作为颜色信息
    if band_index < spectrum.shape[1]:
        colors = spectrum[:, band_index]
        
        # 归一化颜色值到[0,1]范围
        if np.max(colors) > 0:
            colors = colors / np.max(colors)
        
        # 创建3D可视化
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # 使用光谱数据作为颜色信息绘制点云
        sc = ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                       c=colors, cmap='jet', s=1)
        
        wavelength_value = START_WAVELENGTH + (band_index * (END_WAVELENGTH - START_WAVELENGTH) / spectrum.shape[1])
        plt.colorbar(sc, ax=ax, label=f'光谱强度 ({wavelength_value:.1f}nm)')
        
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.set_zlabel('Z轴')
        ax.set_title(f'带有光谱信息的点云 ({wavelength_value:.1f}nm)')
        
        # 设置合适的视角
        ax.view_init(elev=30, azim=45)
        
        plt.tight_layout()
        plt.show()
    else:
        print(f"错误: 波段索引{band_index}超出范围[0,{spectrum.shape[1]-1}]")


def process_and_fuse_data() -> None:
    """
    处理点云数据并与高光谱数据融合的主函数
    """
    print("----- 开始处理点云数据和高光谱融合 -----")
    
    # 步骤1: 处理点云数据
    print("正在加载原始点云数据...")
    points = read_ply(PLY_FILE_PATH)
    print(f"原始点云数据形状: {points.shape}")
    
    # 裁剪点云数据
    print(f"裁剪点云数据至范围 X:{X_LIMITS}, Y:{Y_LIMITS}, Z:{Z_LIMITS}...")
    cropped_points = crop_point_cloud(points, X_LIMITS, Y_LIMITS, Z_LIMITS)
    print(f"裁剪后点云数据形状: {cropped_points.shape}")
    
    # 下采样并填充没有数据的点为0
    print(f"将点云降采样至网格大小: {GRID_SIZE}...")
    x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(
        cropped_points, 
        grid_size=GRID_SIZE,
        x_range=X_RANGE, 
        y_range=Y_RANGE
    )
    print(f"降采样后点云数据形状: {downsampled_points.shape}")
    
    # 去除不符合深度范围的点
    print(f"应用深度阈值过滤: {DEPTH_THRESHOLD}...")
    modified_points = apply_depth_threshold(
        downsampled_points, 
        depth_threshold=DEPTH_THRESHOLD
    )
    print(f"深度过滤后点云数据形状: {modified_points.shape}")
    
    # 步骤2: 加载高光谱数据
    print(f"加载高光谱数据: {HSI_FILE_PATH}")
    try:
        hsi_data = scipy.io.loadmat(HSI_FILE_PATH)["HSI"]
        print(f"高光谱数据形状: {hsi_data.shape}")
    except Exception as e:
        print(f"加载高光谱数据失败: {e}")
        return
    
    # 步骤3: 将点云数据重塑为与高光谱数据相同的空间分辨率
    height, width, bands = hsi_data.shape
    print(f"将点云数据重塑为: {height}x{width}")
    
    # 将点云数据格式从(N,3)转换为(H,W,3)
    # 注意: 这一步需要根据实际点云排列方式调整
    point_cloud_2d = reshape_point_cloud_to_image(modified_points, height, width)
    print(f"重塑后点云数据形状: {point_cloud_2d.shape}")
    
    # 步骤4: 创建融合数据
    fusion_data = create_fusion_data(point_cloud_2d, hsi_data)
    
    # 步骤5: 可视化融合结果
    band_index = get_band_index(TARGET_WAVELENGTH)
    print(f"使用波段索引 {band_index} (对应 {TARGET_WAVELENGTH}nm) 进行可视化")
    visualize_fusion_data(fusion_data, band_index=band_index)
    
    # 步骤6: 保存融合数据
    # 计算波长数组
    wavelengths = np.linspace(START_WAVELENGTH, END_WAVELENGTH, bands)
    
    # 保存为CSV格式
    save_fusion_data_csv(fusion_data, FUSION_OUTPUT_CSV, FUSION_META_CSV, wavelengths)
    
    print("----- 点云和高光谱数据融合完成 -----")


def process_point_cloud() -> None:
    """
    处理点云数据的主函数，包括加载、裁剪、降采样和可视化
    """
    print("正在加载原始点云数据...")
    # 读取点云数据
    points = read_ply(PLY_FILE_PATH)
    print(f"原始点云数据形状: {points.shape}")
    
    # 裁剪点云数据
    print(f"裁剪点云数据至范围 X:{X_LIMITS}, Y:{Y_LIMITS}, Z:{Z_LIMITS}...")
    cropped_points = crop_point_cloud(points, X_LIMITS, Y_LIMITS, Z_LIMITS)
    print(f"裁剪后点云数据形状: {cropped_points.shape}")
    
    # 绘制剪裁后的三视图并保存
    print("生成裁剪后点云的三视图...")
    plot_orthographic_views(cropped_points, 
                           title="裁剪后正交视图",
                           save_path=SAVE_ORTHOGRAPHIC_PATH)
    print(f"三视图已保存至: {SAVE_ORTHOGRAPHIC_PATH}")

    # 下采样并填充没有数据的点为0
    print(f"将点云降采样至网格大小: {GRID_SIZE}...")
    x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(
        cropped_points, 
        grid_size=GRID_SIZE,
        x_range=X_RANGE, 
        y_range=Y_RANGE
    )
    print(f"降采样后点云数据形状: {downsampled_points.shape}")
    
    # 可视化降采样后的点云数据
    print("生成降采样后点云的3D表面图...")
    plot_3d_surface(x_grid, y_grid, z_grid)
    
    # 绘制降采样后的三视图
    print("生成降采样后点云的三视图...")
    plot_orthographic_views(downsampled_points, title="降采样后正交视图")

    # 去除不符合深度范围的点
    print(f"应用深度阈值过滤: {DEPTH_THRESHOLD}...")
    modified_downsampled_points = apply_depth_threshold(
        downsampled_points, 
        depth_threshold=DEPTH_THRESHOLD
    )
    print(f"深度过滤后点云数据形状: {modified_downsampled_points.shape}")
    
    # 绘制三维图和三视图
    print("生成深度过滤后点云的3D图...")
    plot_3d_points(modified_downsampled_points, title="深度过滤后3D视图")
    
    print("生成深度过滤后点云的三视图...")
    plot_orthographic_views(modified_downsampled_points, title="深度过滤后正交视图")
    
    print("点云处理完成!")
    
    #------ 结合高光谱图像看深度图的的大小是否正确
    hsi_image_resized = scipy.io.loadmat(HSI_FILE_PATH)["HSI"]
    # 将波段改为目标波长
    band_index = get_band_index(TARGET_WAVELENGTH)
    print(f"使用波段索引 {band_index} (对应 {TARGET_WAVELENGTH}nm)")

    # 应用深度到高光谱图像
    hsi_image_resized1 = apply_depth_to_hsi(modified_downsampled_points, hsi_image_resized)
    plot_hyperspectral_band_overlay(hsi_image_resized, hsi_image_resized1, band_index=band_index)

    # 保存点云数据
    save_point_cloud_open3d(modified_downsampled_points, SAVE_FILE_PATH)
    print(f"点云数据已保存至: {SAVE_FILE_PATH}")


def save_fusion_data_csv(fusion_data: np.ndarray, 
                        file_path: str, 
                        meta_path: str,
                        wavelengths: Optional[np.ndarray] = None) -> None:
    """
    将融合数据保存为CSV格式
    
    参数:
        fusion_data: np.ndarray - 融合数据，形状为(H, W, 3+B)
        file_path: str - 数据CSV保存路径
        meta_path: str - 元数据CSV保存路径
        wavelengths: Optional[np.ndarray] - 波长数组，如果不提供则根据START_WAVELENGTH和END_WAVELENGTH计算
    
    返回:
        None
    """
    # 创建目录（如果不存在）
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 获取数据维度
    height, width, channels = fusion_data.shape
    bands = channels - 3  # 前3个通道是XYZ坐标
    
    # 计算波长数组（如果未提供）
    if wavelengths is None:
        wavelengths = np.linspace(START_WAVELENGTH, END_WAVELENGTH, bands)
    
    print(f"准备将融合数据保存为CSV格式，形状: {fusion_data.shape}")
    
    # 创建列名
    column_names = ['row', 'col', 'x', 'y', 'z']
    for i, wl in enumerate(wavelengths):
        column_names.append(f'band_{i}_{wl:.1f}nm')
    
    # 创建DataFrame
    data_rows = []
    for i in range(height):
        for j in range(width):
            # 获取当前像素的所有数据
            pixel_data = fusion_data[i, j, :]
            
            # 跳过Z坐标为0的点（可选，取决于数据特性）
            if pixel_data[2] == 0:
                continue
                
            # 创建行数据：位置、XYZ坐标、所有波段值
            row_data = [i, j] + list(pixel_data)
            data_rows.append(row_data)
    
    # 转换为DataFrame并保存
    df = pd.DataFrame(data_rows, columns=column_names)
    df.to_csv(file_path, index=False)
    print(f"融合数据已保存为CSV: {file_path}")
    print(f"CSV包含 {len(df)} 行有效数据点")
    
    # 保存元数据
    meta_data = {
        'height': [height],
        'width': [width],
        'bands': [bands],
        'has_xyz': [True],
        'xyz_channels': [3],
        'start_wavelength': [START_WAVELENGTH],
        'end_wavelength': [END_WAVELENGTH],
        'description': ['融合的点云和高光谱数据，前3个通道为XYZ坐标，后续通道为光谱数据']
    }
    meta_df = pd.DataFrame(meta_data)
    meta_df.to_csv(meta_path, index=False)
    print(f"元数据已保存为CSV: {meta_path}")


def load_fusion_data_csv(file_path: str, meta_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    从CSV文件加载融合数据
    
    参数:
        file_path: str - 数据CSV文件路径
        meta_path: str - 元数据CSV文件路径
    
    返回:
        Tuple[np.ndarray, np.ndarray] - (融合数据, 波长数组)
    """
    # 加载元数据
    meta_df = pd.read_csv(meta_path)
    height = meta_df['height'].iloc[0]
    width = meta_df['width'].iloc[0]
    bands = meta_df['bands'].iloc[0]
    start_wavelength = meta_df['start_wavelength'].iloc[0]
    end_wavelength = meta_df['end_wavelength'].iloc[0]
    
    # 加载数据
    df = pd.read_csv(file_path)
    
    # 提取波长信息
    band_columns = [col for col in df.columns if col.startswith('band_')]
    wavelengths = np.array([float(col.split('_')[2].replace('nm', '')) for col in band_columns])
    
    # 创建融合数据数组
    fusion_data = np.zeros((height, width, 3 + bands))
    
    # 填充数据
    for _, row in df.iterrows():
        i, j = int(row['row']), int(row['col'])
        if 0 <= i < height and 0 <= j < width:
            # 填充XYZ坐标
            fusion_data[i, j, 0:3] = [row['x'], row['y'], row['z']]
            
            # 填充光谱数据
            for b, band_col in enumerate(band_columns):
                fusion_data[i, j, 3 + b] = row[band_col]
    
    print(f"从CSV加载融合数据，形状: {fusion_data.shape}")
    print(f"波长范围: {wavelengths[0]:.1f}nm - {wavelengths[-1]:.1f}nm")
    
    return fusion_data, wavelengths


if __name__ == "__main__":
    # 选择要执行的操作
    print("请选择要执行的操作:")
    print("1. 仅处理点云数据")
    print("2. 处理并融合点云和高光谱数据")
    
    choice = input("输入选项编号 (默认为1): ").strip()
    
    if choice == "2":
        process_and_fuse_data()
    else:
        process_point_cloud()
