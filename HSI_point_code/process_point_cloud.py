import os
from matplotlib import pyplot as plt
import numpy as np
import scipy.io
from osgeo import gdal_array

from utils import apply_depth_threshold, apply_depth_to_hsi, crop_point_cloud, downsample_and_fill, plot_3d_points, plot_3d_surface, plot_hyperspectral_band_overlay, plot_orthographic_views, read_ply, save_point_cloud_open3d, visualize_point_cloud


# 绘制三视图（XY、XZ、YZ）
def plot_orthographic_views(points, title=None, save_path=None):
    """
    Plot orthographic views of point cloud
    
    Parameters:
    points: Point cloud coordinates (N, 3)
    title: Title for the plot (optional)
    save_path: Path to save the visualization (optional)
    """
    fig = plt.figure(figsize=(15, 5))
    
    # 设置透明背景
    fig.patch.set_alpha(0)
    
    # Top view
    ax1 = fig.add_subplot(131)
    scatter1 = ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter1, ax=ax1, label='Z')
    ax1.set_title('Top View (XY)')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.grid(False)  # 移除网格线
    ax1.axis('equal')
    ax1.patch.set_alpha(0)  # 设置子图背景透明
    
    # Front view
    ax2 = fig.add_subplot(132)
    scatter2 = ax2.scatter(points[:, 0], points[:, 2], c=points[:, 1], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter2, ax=ax2, label='Y')
    ax2.set_title('Front View (XZ)')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Z')
    ax2.grid(False)  # 移除网格线
    ax2.patch.set_alpha(0)  # 设置子图背景透明
    
    # Side view
    ax3 = fig.add_subplot(133)
    scatter3 = ax3.scatter(points[:, 1], points[:, 2], c=points[:, 0], 
                          cmap='viridis', s=1)
    plt.colorbar(scatter3, ax=ax3, label='X')
    ax3.set_title('Side View (YZ)')
    ax3.set_xlabel('Y')
    ax3.set_ylabel('Z')
    ax3.grid(False)  # 移除网格线
    ax3.patch.set_alpha(0)  # 设置子图背景透明
    
    if title:
        fig.suptitle(title)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path:
        plt.savefig(save_path, 
                   dpi=300, 
                   bbox_inches='tight',
                   transparent=True,  # 保存为透明背景
                   format='png')      # 指定保存格式为PNG
        plt.close()
    else:
        plt.show()
        
# 绘制三维图
def plot_3d_points(points, title="3D View"):
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    sc = ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=points[:, 2], cmap='viridis')
    fig.colorbar(sc, ax=ax, label='Z Value')
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    
    # 隐藏z轴
    ax.set_zticks([])  # 移除z轴刻度
    ax.zaxis.line.set_visible(False)  # 隐藏z轴线
    
    # 设置观察角度为俯视图
    ax.view_init(elev=90, azim=-90)
    
    ax.set_title(title)
    ax.invert_zaxis()
    plt.show()
        
# 叠加显示处理前和处理后的高光谱图像在指定波段的灰度图
def plot_hyperspectral_band_overlay(original_hsi, processed_hsi, band_index):
    """
    Plot overlay of original and processed hyperspectral images at specific wavelength.

    Parameters:
        original_hsi (ndarray): Original hyperspectral image data (H, W, B)
        processed_hsi (ndarray): Processed hyperspectral image data (H, W, B)
        band_index (int): Band index to display
    """
    # 提取指定波段的图像
    original_band = original_hsi[:, :, band_index]
    processed_band = processed_hsi[:, :, band_index]

    # 计算实际波长
    wavelength = 1000 + (band_index * (2500-1000)/272)

    # 创建叠加显示
    plt.figure(figsize=(8, 6))
    plt.imshow(original_band, cmap='jet', label='Original', vmin=0, vmax=8000)
    plt.imshow(processed_band, cmap='gray', alpha=0.5, label='Processed', vmin=0, vmax=8000)

    plt.colorbar(label="Spectral Intensity")
    plt.title(f"Hyperspectral Image at {wavelength:.0f}nm - Original vs Processed")
    plt.xlabel('Width (pixels)')
    plt.ylabel('Height (pixels)')
    plt.axis('on')
    plt.legend(['Original', 'Processed'], loc='upper right')
    plt.show()
""" # 点云数据下采样，同时将范围 x_range=(-20, 0), y_range=(0, 10)的深度为0位置取周围的深度填充
def downsample_and_fill(points, grid_size=(105, 88)):
    x, y, z = points[:, 0], points[:, 1], points[:, 2]

    # 创建目标网格的x, y坐标
    x_min, x_max = np.min(x), np.max(x)
    y_min, y_max = np.min(y), np.max(y)

    x_grid = np.linspace(x_min, x_max, grid_size[0])
    y_grid = np.linspace(y_min, y_max, grid_size[1])

    # 初始化 z_grid，尺寸为 (105, 88)，并填充为 0
    z_grid = np.zeros((grid_size[0], grid_size[1]))
    # 初始化 z_grid，尺寸为 (105, 88)，并填充为 500
    # z_grid = np.full((grid_size[0], grid_size[1]), 420)

    # 将点云数据映射到网格上
    for i in range(len(points)):
        x_idx = np.searchsorted(x_grid, x[i]) - 1
        y_idx = np.searchsorted(y_grid, y[i]) - 1

        # 确保索引在有效范围内
        x_idx = np.clip(x_idx, 0, grid_size[0] - 1)
        y_idx = np.clip(y_idx, 0, grid_size[1] - 1)

        # 填充 z 值
        z_grid[x_idx, y_idx] = z[i]

    # 识别出给定 x 范围和 y 范围中的缺失点
    # x_mask = (x_grid >= x_range[0]) & (x_grid <= x_range[1])
    # y_mask = (y_grid >= y_range[0]) & (y_grid <= y_range[1])
    # center_mask = np.outer(x_mask, y_mask)
    # missing_points = (z_grid == 0) & center_mask

    # # 遍历缺失点并用左右的非空点进行填充
    # for i in range(1, grid_size[0] - 1):
    #     for j in range(1, grid_size[1] - 1):
    #         if missing_points[i, j]:  # 如果是缺失点
    #             # 获取左右的非空值
    #             left_value = z_grid[i, j - 1] if z_grid[i, j - 1] != 0 else None
    #             right_value = z_grid[i, j + 1] if z_grid[i, j + 1] != 0 else None

    #             # 计算填充值，取非空值的平均
    #             values = [v for v in [left_value, right_value] if v is not None]
    #             if values:
    #                 z_grid[i, j] = np.mean(values)

    # 将 z_grid 转换为 (105*88, 3) 的点云格式
    sampled_points = []
    for i in range(grid_size[0]):
        for j in range(grid_size[1]):
            sampled_points.append([x_grid[i], y_grid[j], z_grid[i, j]])

    return x_grid, y_grid, z_grid, np.array(sampled_points) """

ply_file = r'/Users/<USER>/Desktop/研究生学业/高光谱原理解析/cloud_point_apple_data/250989/DepthPoints_250989.ply'  # 替换为你的 PLY 文件路径
save_file = r"/Users/<USER>/Desktop/研究生学业/高光谱原理解析/cloud_point_apple_data/250989/resized_cloudpoint.ply"
x_limits = (-10, 80)
y_limits = (-33, 60)
z_limits = (300, 850)

# ply_file = r'/Users/<USER>/Desktop/研究生学业/高光谱原理解析/cloud_point_apple_data/257669/DepthPoints_257669.ply'  # 替换为你的 PLY 文件路径
# save_file = r"/Users/<USER>/Desktop/研究生学业/高光谱原理解析/cloud_point_apple_data/257669/resized_cloudpoint.ply"
# 读取点云数据
points = read_ply(ply_file)
print("LOAD original point cloud...")
# 上下
# x_limits = (-20, 60)
# y_limits = (-35, 50)
# z_limits = (300, 850)
# 裁剪点云数据
cropped_points = crop_point_cloud(points, x_limits, y_limits, z_limits)
# 点云数据沿 x 轴翻转
cropped_points[:,1] = -cropped_points[:,1]
# 绘制剪裁后的三视图并保存
plot_orthographic_views(cropped_points, 
                       title="Cropped Orthographic Views",
                       save_path="./orthographic_views.png")

# 下采样并填充没有数据的点为0
x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(cropped_points, grid_size=(100,76),x_range=(-23, 37), y_range=(11, 23))
# x_grid, y_grid, z_grid, downsampled_points = downsample_and_fill(cropped_points, grid_size=(108, 89), x_range=(-40, 0), y_range=(-30, 20))
# 可视化降采样后的点云数据
plot_3d_surface(x_grid, y_grid, z_grid)
# 绘制降采样后的三视图（XY、XZ、YZ）
plot_orthographic_views(downsampled_points, title="Cropped Orthographic Views")

# 去除不符合深度范围深度
modified_downsampled_points = apply_depth_threshold(downsampled_points, depth_threshold=450)
# 绘制三维图
plot_3d_points(modified_downsampled_points, title="Cropped 3D View")
# 绘制三视图（XY、XZ、YZ）
plot_orthographic_views(modified_downsampled_points, title="Cropped Orthographic Views")

def get_band_index(wavelength, start_nm=1000, end_nm=2500, total_bands=273):
    """
    Calculate band index for given wavelength
    """
    step = (end_nm - start_nm) / (total_bands - 1)
    index = int((wavelength - start_nm) / step)
    return min(max(0, index), total_bands - 1)

#------ 结合高光谱图像看深度图的的大小是否正确
# hsi_image_resized = scipy.io.loadmat("data/241111/3_apple/resized_HSI.mat")["HSI"]
hsi_image_resized = scipy.io.loadmat("data/0107/第一个/resized_HSI.mat")["HSI"]
# 将波段改为1200nm
target_wavelength = 1200
band_index = get_band_index(target_wavelength)
print(f"Using band index {band_index} corresponding to {target_wavelength}nm")

hsi_image_resized1 = apply_depth_to_hsi(modified_downsampled_points, hsi_image_resized)
plot_hyperspectral_band_overlay(hsi_image_resized, hsi_image_resized1, band_index=band_index)

# 保存点云数据
save_point_cloud_open3d(modified_downsampled_points, save_file) 
# # # 保存去除背景的高光谱数据
# # scipy.io.savemat(f"data/241111/3_apple/resized_0_HSI.mat", {"HSI": hsi_image_resized1})
# # gdal_array.SaveArray(hsi_image_resized1.transpose(2, 0, 1), f"data/241111/3_apple/resized_0_HSI.tif", format="GTiff")

